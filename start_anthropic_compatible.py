#!/usr/bin/env python3
"""
启动ANTHROPIC兼容的Ollama API服务
"""

import os
import sys
import time
import subprocess
import signal
import requests
from pathlib import Path

# API Key
API_KEY = "ollama-qofpBPKHark0yGUSJ4E_wy3esdfulc5j"

def check_ollama_service():
    """检查Ollama服务"""
    try:
        response = requests.get("http://127.0.0.1:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama服务运行中，可用模型: {len(models)}")
            return True
        return False
    except:
        print("❌ Ollama服务未运行，请先启动: ollama serve")
        return False

def start_ollama_api():
    """启动Ollama API服务"""
    print("🚀 启动Ollama API服务...")
    
    # 设置环境变量
    env = os.environ.copy()
    env["ANTHROPIC_AUTH_TOKEN"] = API_KEY
    env["ANTHROPIC_BASE_URL"] = "http://127.0.0.1:8000"
    
    process = subprocess.Popen([
        sys.executable, "simple_start.py"
    ], env=env)
    
    # 等待服务启动
    for i in range(10):
        try:
            response = requests.get("http://127.0.0.1:8000/health", timeout=2)
            if response.status_code == 200:
                print("✅ Ollama API服务启动成功")
                return process
        except:
            pass
        time.sleep(1)
    
    print("❌ Ollama API服务启动失败")
    process.terminate()
    return None

def start_anthropic_adapter():
    """启动ANTHROPIC兼容适配器"""
    print("🔗 启动ANTHROPIC兼容适配器...")
    
    # 设置环境变量
    env = os.environ.copy()
    env["ANTHROPIC_AUTH_TOKEN"] = API_KEY
    
    process = subprocess.Popen([
        sys.executable, "anthropic_adapter.py",
        "--api-key", API_KEY,
        "--host", "127.0.0.1",
        "--port", "8001",
        "--ollama-url", "http://127.0.0.1:8000"
    ], env=env)
    
    # 等待服务启动
    for i in range(10):
        try:
            response = requests.get("http://127.0.0.1:8001/", timeout=2)
            if response.status_code == 200:
                print("✅ ANTHROPIC兼容适配器启动成功")
                return process
        except:
            pass
        time.sleep(1)
    
    print("❌ ANTHROPIC兼容适配器启动失败")
    process.terminate()
    return None

def test_anthropic_api():
    """测试ANTHROPIC兼容API"""
    print("\n🧪 测试ANTHROPIC兼容API...")
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    # 测试模型列表
    try:
        response = requests.get("http://127.0.0.1:8001/v1/models", headers=headers, timeout=10)
        if response.status_code == 200:
            models = response.json()
            print(f"✅ 模型列表获取成功: {len(models.get('data', []))} 个模型")
        else:
            print(f"❌ 模型列表获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 模型列表测试失败: {str(e)}")
        return False
    
    # 测试消息创建
    try:
        data = {
            "model": "gemma3:1b",
            "max_tokens": 50,
            "messages": [
                {"role": "user", "content": "Hello, please respond with 'ANTHROPIC API test successful'"}
            ]
        }
        
        response = requests.post("http://127.0.0.1:8001/v1/messages", json=data, headers=headers, timeout=30)
        if response.status_code == 200:
            result = response.json()
            content = result.get("content", [{}])[0].get("text", "")
            print(f"✅ 消息创建成功: {content[:50]}...")
            return True
        else:
            print(f"❌ 消息创建失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 消息创建测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔗 启动ANTHROPIC兼容的Ollama API服务")
    print("=" * 60)
    
    # 检查Ollama服务
    if not check_ollama_service():
        return
    
    processes = []
    
    try:
        # 启动Ollama API服务
        ollama_process = start_ollama_api()
        if not ollama_process:
            return
        processes.append(ollama_process)
        
        time.sleep(2)
        
        # 启动ANTHROPIC兼容适配器
        adapter_process = start_anthropic_adapter()
        if not adapter_process:
            return
        processes.append(adapter_process)
        
        time.sleep(2)
        
        # 测试API
        if test_anthropic_api():
            print("\n🎉 所有服务启动成功!")
            print("\n📋 ANTHROPIC兼容环境变量:")
            print(f"export ANTHROPIC_BASE_URL=http://127.0.0.1:8001")
            print(f"export ANTHROPIC_AUTH_TOKEN={API_KEY}")
            print("\n🌐 API端点:")
            print("   POST http://127.0.0.1:8001/v1/messages")
            print("   GET  http://127.0.0.1:8001/v1/models")
            print("\n📝 使用示例:")
            print(f'curl -X POST "http://127.0.0.1:8001/v1/messages" \\')
            print(f'     -H "Authorization: Bearer {API_KEY}" \\')
            print(f'     -H "Content-Type: application/json" \\')
            print(f'     -d \'{{"model": "gemma3:1b", "max_tokens": 100, "messages": [{{"role": "user", "content": "Hello"}}]}}\'')
            print("\n⏹️  按 Ctrl+C 停止所有服务")
            
            # 等待用户中断
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 正在停止服务...")
        else:
            print("\n❌ API测试失败")
    
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务...")
    
    finally:
        # 停止所有进程
        for process in processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                try:
                    process.kill()
                except:
                    pass
        print("✅ 所有服务已停止")

if __name__ == "__main__":
    main()
