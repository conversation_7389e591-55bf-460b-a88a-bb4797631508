"""
配置文件
"""

import os

# API服务配置
API_HOST = "127.0.0.1"
API_PORT = 8000
API_BASE_URL = f"http://{API_HOST}:{API_PORT}"

# Ollama配置
OLLAMA_HOST = "127.0.0.1"
OLLAMA_PORT = 11434
OLLAMA_BASE_URL = f"http://{OLLAMA_HOST}:{OLLAMA_PORT}"

# 默认模型
DEFAULT_MODEL = "llama2"

# 请求超时设置
REQUEST_TIMEOUT = 60

# 日志配置
LOG_LEVEL = "INFO"

# API Key配置
# 可以通过环境变量或命令行参数设置
API_KEY = os.getenv("OLLAMA_API_KEY", "")
API_KEY_HEADER = "X-API-Key"

# 默认API Key（仅用于演示，生产环境请使用强密码）
DEFAULT_API_KEY = "ollama-demo-key-2024"
