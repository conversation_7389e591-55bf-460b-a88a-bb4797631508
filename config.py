"""
配置文件
"""

import os

# API服务配置
API_HOST = "127.0.0.1"
API_PORT = 8000
API_BASE_URL = f"http://{API_HOST}:{API_PORT}"

# Ollama配置
OLLAMA_HOST = "127.0.0.1"
OLLAMA_PORT = 11434
OLLAMA_BASE_URL = f"http://{OLLAMA_HOST}:{OLLAMA_PORT}"

# 默认模型
DEFAULT_MODEL = "llama2"

# 请求超时设置
REQUEST_TIMEOUT = 60

# 日志配置
LOG_LEVEL = "INFO"

# API Key配置
# 支持多种环境变量格式，兼容ANTHROPIC格式
API_KEY = (
    os.getenv("ANTHROPIC_AUTH_TOKEN") or  # 优先使用ANTHROPIC格式
    os.getenv("ANTHROPIC_API_KEY") or     # ANTHROPIC备用格式
    os.getenv("OLLAMA_AUTH_TOKEN") or     # 我们的格式
    os.getenv("OLLAMA_API_KEY") or        # 我们的备用格式
    ""
)

# API服务URL配置，支持ANTHROPIC格式
API_BASE_URL = (
    os.getenv("ANTHROPIC_BASE_URL") or    # 优先使用ANTHROPIC格式
    os.getenv("OLLAMA_BASE_URL") or       # 我们的格式
    f"http://{API_HOST}:{API_PORT}"       # 默认值
)

API_KEY_HEADER = "X-API-Key"

# 默认API Key（仅用于演示，生产环境请使用强密码）
DEFAULT_API_KEY = "ollama-demo-key-2024"
