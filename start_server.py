#!/usr/bin/env python3
"""
启动脚本 - 检查依赖并启动API服务器
"""

import subprocess
import sys
import time
import requests
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    print(f"✓ Python版本: {sys.version}")

def install_dependencies():
    """安装依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ 依赖包安装完成")
    except subprocess.CalledProcessError:
        print("错误: 依赖包安装失败")
        sys.exit(1)

def check_ollama_service():
    """检查Ollama服务是否运行"""
    print("检查Ollama服务...")
    try:
        response = requests.get("http://127.0.0.1:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✓ Ollama服务运行中，可用模型数量: {len(models)}")
            if models:
                print("  可用模型:")
                for model in models[:3]:  # 只显示前3个
                    print(f"    - {model['name']}")
            return True
        else:
            print("⚠ Ollama服务响应异常")
            return False
    except requests.exceptions.RequestException:
        print("⚠ Ollama服务未运行")
        print("请先启动Ollama服务:")
        print("  1. 安装Ollama: curl -fsSL https://ollama.ai/install.sh | sh")
        print("  2. 下载模型: ollama pull llama2")
        print("  3. 启动服务: ollama serve")
        return False

def start_api_server():
    """启动API服务器"""
    print("启动API服务器...")
    try:
        # 使用subprocess启动服务器
        process = subprocess.Popen([
            sys.executable, "api_server.py"
        ])
        
        # 等待服务器启动
        print("等待服务器启动...")
        time.sleep(3)
        
        # 检查服务器是否启动成功
        try:
            response = requests.get("http://127.0.0.1:8000/health", timeout=5)
            if response.status_code == 200:
                print("✓ API服务器启动成功")
                print("  服务地址: http://127.0.0.1:8000")
                print("  API文档: http://127.0.0.1:8000/docs")
                return process
            else:
                print("⚠ API服务器启动异常")
                process.terminate()
                return None
        except requests.exceptions.RequestException:
            print("⚠ API服务器启动失败")
            process.terminate()
            return None
            
    except Exception as e:
        print(f"错误: 启动API服务器失败 - {str(e)}")
        return None

def main():
    """主函数"""
    print("=== Ollama API 服务启动器 ===\n")
    
    # 1. 检查Python版本
    check_python_version()
    
    # 2. 检查项目文件
    required_files = ["api_server.py", "client.py", "business_logic.py", "config.py", "requirements.txt"]
    missing_files = [f for f in required_files if not Path(f).exists()]
    
    if missing_files:
        print(f"错误: 缺少必要文件: {', '.join(missing_files)}")
        sys.exit(1)
    print("✓ 项目文件检查完成")
    
    # 3. 安装依赖
    install_dependencies()
    
    # 4. 检查Ollama服务
    ollama_running = check_ollama_service()
    
    # 5. 启动API服务器
    server_process = start_api_server()
    
    if server_process:
        print("\n=== 服务启动完成 ===")
        print("可以使用以下命令测试:")
        print("  python client.py          # 客户端测试")
        print("  python business_logic.py  # 业务逻辑测试")
        print("\n按 Ctrl+C 停止服务器")
        
        try:
            server_process.wait()
        except KeyboardInterrupt:
            print("\n正在停止服务器...")
            server_process.terminate()
            server_process.wait()
            print("服务器已停止")
    else:
        print("\n启动失败，请检查错误信息")
        if not ollama_running:
            print("提示: 请确保Ollama服务正在运行")

if __name__ == "__main__":
    main()
