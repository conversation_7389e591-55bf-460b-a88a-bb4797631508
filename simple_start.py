#!/usr/bin/env python3
"""
简化启动脚本
"""

import os
import sys

# 设置API Key
API_KEY = "ollama-qofpBPKHark0yGUSJ4E_wy3esdfulc5j"
os.environ["OLLAMA_API_KEY"] = API_KEY

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入并启动服务器
from api_server import main

if __name__ == "__main__":
    print(f"🔑 Starting Ollama API Server with API Key: {API_KEY}")
    print(f"📝 Save this key for client authentication!")
    
    # 设置命令行参数
    sys.argv = [
        "api_server.py",
        "--api-key", API_KEY,
        "--host", "127.0.0.1", 
        "--port", "8000"
    ]
    
    main()
