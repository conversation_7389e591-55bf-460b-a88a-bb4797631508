"""
HTTP客户端调用逻辑
"""

import json
import logging
import requests
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from config import REQUEST_TIMEOUT, DEFAULT_MODEL, API_KEY_HEADER
import os

# 支持ANTHROPIC格式的环境变量
def get_api_base_url():
    return (
        os.getenv("ANTHROPIC_BASE_URL") or
        os.getenv("OLLAMA_BASE_URL") or
        "http://127.0.0.1:8000"
    )

def get_api_key():
    return (
        os.getenv("ANTHROPIC_AUTH_TOKEN") or
        os.getenv("ANTHROPIC_API_KEY") or
        os.getenv("OLLAMA_AUTH_TOKEN") or
        os.getenv("OLLAMA_API_KEY") or
        ""
    )

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ChatRequest:
    """聊天请求数据类"""
    message: str
    model: str = DEFAULT_MODEL
    temperature: float = 0.7
    max_tokens: int = 1000

@dataclass
class ChatResponse:
    """聊天响应数据类"""
    response: str
    status: str
    model: str
    error: Optional[str] = None

class OllamaClient:
    """Ollama API客户端"""

    def __init__(self, base_url: Optional[str] = None, timeout: int = REQUEST_TIMEOUT, api_key: Optional[str] = None):
        # 自动获取base_url和api_key，支持ANTHROPIC格式
        self.base_url = (base_url or get_api_base_url()).rstrip('/')
        self.timeout = timeout
        self.api_key = api_key or get_api_key()
        self.session = requests.Session()

        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })

        # 如果提供了API Key，添加到请求头
        if self.api_key:
            self.session.headers.update({
                API_KEY_HEADER: self.api_key,
                'Authorization': f'Bearer {self.api_key}'
            })
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """发送HTTP请求的通用方法"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            logger.info(f"Making {method} request to {url}")
            
            if method.upper() == 'GET':
                response = self.session.get(url, timeout=self.timeout)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data, timeout=self.timeout)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            # 检查HTTP状态码
            response.raise_for_status()
            
            # 解析JSON响应
            result = response.json()
            logger.info(f"Request successful, status: {result.get('status', 'unknown')}")
            
            return result
            
        except requests.exceptions.Timeout:
            error_msg = f"Request timeout after {self.timeout} seconds"
            logger.error(error_msg)
            return {"status": "error", "error": error_msg}
            
        except requests.exceptions.ConnectionError:
            error_msg = f"Failed to connect to {url}"
            logger.error(error_msg)
            return {"status": "error", "error": error_msg}
            
        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP error {e.response.status_code}: {e.response.text}"
            logger.error(error_msg)
            return {"status": "error", "error": error_msg}
            
        except json.JSONDecodeError:
            error_msg = "Invalid JSON response from server"
            logger.error(error_msg)
            return {"status": "error", "error": error_msg}
            
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            logger.error(error_msg)
            return {"status": "error", "error": error_msg}
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        # 如果是ANTHROPIC兼容端点，使用根路径
        if "8001" in self.base_url:
            return self._make_request('GET', '/')
        else:
            return self._make_request('GET', '/health')
    
    def get_models(self) -> List[str]:
        """获取可用模型列表"""
        # 如果是ANTHROPIC兼容端点，使用v1/models
        if "8001" in self.base_url:
            result = self._make_request('GET', '/v1/models')
            if result.get('object') == 'list':
                return [model['id'] for model in result.get('data', [])]
            else:
                logger.error(f"Failed to get models: {result.get('error', 'Unknown error')}")
                return []
        else:
            result = self._make_request('GET', '/models')
            if result.get('status') == 'success':
                return result.get('models', [])
            else:
                logger.error(f"Failed to get models: {result.get('error', 'Unknown error')}")
                return []
    
    def chat(self, request: ChatRequest) -> ChatResponse:
        """发送聊天请求"""
        data = {
            'message': request.message,
            'model': request.model,
            'temperature': request.temperature,
            'max_tokens': request.max_tokens
        }
        
        result = self._make_request('POST', '/chat', data)
        
        return ChatResponse(
            response=result.get('response', ''),
            status=result.get('status', 'error'),
            model=result.get('model', request.model),
            error=result.get('error')
        )
    
    def generate(self, request: ChatRequest) -> ChatResponse:
        """发送文本生成请求"""
        data = {
            'message': request.message,
            'model': request.model,
            'temperature': request.temperature,
            'max_tokens': request.max_tokens
        }
        
        result = self._make_request('POST', '/generate', data)
        
        return ChatResponse(
            response=result.get('response', ''),
            status=result.get('status', 'error'),
            model=result.get('model', request.model),
            error=result.get('error')
        )

def main():
    """客户端测试主函数"""
    import os
    import sys

    # 自动获取API Key，支持ANTHROPIC格式
    api_key = get_api_key()
    base_url = get_api_base_url()

    if not api_key:
        print("⚠️  No API Key found in environment variables")
        print("    Supported variables: ANTHROPIC_AUTH_TOKEN, ANTHROPIC_API_KEY, OLLAMA_AUTH_TOKEN, OLLAMA_API_KEY")
        api_key = input("Please enter your API Key (or press Enter to skip): ").strip()
        if not api_key:
            print("⚠️  Running without API Key - this may fail if server requires authentication")

    client = OllamaClient()  # 自动使用环境变量

    print("=== Ollama API 客户端测试 ===")
    print(f"🌐 Base URL: {base_url}")
    if api_key:
        print(f"🔑 Using API Key: {api_key[:10]}...{api_key[-4:]}")
    else:
        print("⚠️  No API Key provided")
    print()
    
    # 1. 健康检查
    print("1. 健康检查...")
    health = client.health_check()
    print(f"健康状态: {health.get('status')} - {health.get('message', health.get('error'))}\n")
    
    # 2. 获取模型列表
    print("2. 获取可用模型...")
    models = client.get_models()
    if models:
        print(f"可用模型: {', '.join(models)}\n")
    else:
        print("未找到可用模型\n")
        return
    
    # 3. 聊天测试
    print("3. 聊天测试...")
    chat_request = ChatRequest(
        message="你好，请简单介绍一下你自己。",
        model=models[0] if models else DEFAULT_MODEL,
        temperature=0.7,
        max_tokens=200
    )
    
    chat_response = client.chat(chat_request)
    
    if chat_response.status == 'success':
        print(f"模型: {chat_response.model}")
        print(f"回答: {chat_response.response}\n")
    else:
        print(f"聊天失败: {chat_response.error}\n")
    
    # 4. 文本生成测试
    print("4. 文本生成测试...")
    generate_request = ChatRequest(
        message="写一首关于春天的短诗",
        model=models[0] if models else DEFAULT_MODEL,
        temperature=0.8,
        max_tokens=150
    )
    
    generate_response = client.generate(generate_request)
    
    if generate_response.status == 'success':
        print(f"模型: {generate_response.model}")
        print(f"生成内容: {generate_response.response}")
    else:
        print(f"生成失败: {generate_response.error}")

if __name__ == "__main__":
    main()
