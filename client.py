"""
HTTP客户端调用逻辑
"""

import json
import logging
import requests
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from config import API_BASE_URL, REQUEST_TIMEOUT, DEFAULT_MODEL

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ChatRequest:
    """聊天请求数据类"""
    message: str
    model: str = DEFAULT_MODEL
    temperature: float = 0.7
    max_tokens: int = 1000

@dataclass
class ChatResponse:
    """聊天响应数据类"""
    response: str
    status: str
    model: str
    error: Optional[str] = None

class OllamaClient:
    """Ollama API客户端"""
    
    def __init__(self, base_url: str = API_BASE_URL, timeout: int = REQUEST_TIMEOUT):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """发送HTTP请求的通用方法"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            logger.info(f"Making {method} request to {url}")
            
            if method.upper() == 'GET':
                response = self.session.get(url, timeout=self.timeout)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data, timeout=self.timeout)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            # 检查HTTP状态码
            response.raise_for_status()
            
            # 解析JSON响应
            result = response.json()
            logger.info(f"Request successful, status: {result.get('status', 'unknown')}")
            
            return result
            
        except requests.exceptions.Timeout:
            error_msg = f"Request timeout after {self.timeout} seconds"
            logger.error(error_msg)
            return {"status": "error", "error": error_msg}
            
        except requests.exceptions.ConnectionError:
            error_msg = f"Failed to connect to {url}"
            logger.error(error_msg)
            return {"status": "error", "error": error_msg}
            
        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP error {e.response.status_code}: {e.response.text}"
            logger.error(error_msg)
            return {"status": "error", "error": error_msg}
            
        except json.JSONDecodeError:
            error_msg = "Invalid JSON response from server"
            logger.error(error_msg)
            return {"status": "error", "error": error_msg}
            
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            logger.error(error_msg)
            return {"status": "error", "error": error_msg}
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return self._make_request('GET', '/health')
    
    def get_models(self) -> List[str]:
        """获取可用模型列表"""
        result = self._make_request('GET', '/models')
        
        if result.get('status') == 'success':
            return result.get('models', [])
        else:
            logger.error(f"Failed to get models: {result.get('error', 'Unknown error')}")
            return []
    
    def chat(self, request: ChatRequest) -> ChatResponse:
        """发送聊天请求"""
        data = {
            'message': request.message,
            'model': request.model,
            'temperature': request.temperature,
            'max_tokens': request.max_tokens
        }
        
        result = self._make_request('POST', '/chat', data)
        
        return ChatResponse(
            response=result.get('response', ''),
            status=result.get('status', 'error'),
            model=result.get('model', request.model),
            error=result.get('error')
        )
    
    def generate(self, request: ChatRequest) -> ChatResponse:
        """发送文本生成请求"""
        data = {
            'message': request.message,
            'model': request.model,
            'temperature': request.temperature,
            'max_tokens': request.max_tokens
        }
        
        result = self._make_request('POST', '/generate', data)
        
        return ChatResponse(
            response=result.get('response', ''),
            status=result.get('status', 'error'),
            model=result.get('model', request.model),
            error=result.get('error')
        )

def main():
    """客户端测试主函数"""
    client = OllamaClient()
    
    print("=== Ollama API 客户端测试 ===\n")
    
    # 1. 健康检查
    print("1. 健康检查...")
    health = client.health_check()
    print(f"健康状态: {health.get('status')} - {health.get('message', health.get('error'))}\n")
    
    # 2. 获取模型列表
    print("2. 获取可用模型...")
    models = client.get_models()
    if models:
        print(f"可用模型: {', '.join(models)}\n")
    else:
        print("未找到可用模型\n")
        return
    
    # 3. 聊天测试
    print("3. 聊天测试...")
    chat_request = ChatRequest(
        message="你好，请简单介绍一下你自己。",
        model=models[0] if models else DEFAULT_MODEL,
        temperature=0.7,
        max_tokens=200
    )
    
    chat_response = client.chat(chat_request)
    
    if chat_response.status == 'success':
        print(f"模型: {chat_response.model}")
        print(f"回答: {chat_response.response}\n")
    else:
        print(f"聊天失败: {chat_response.error}\n")
    
    # 4. 文本生成测试
    print("4. 文本生成测试...")
    generate_request = ChatRequest(
        message="写一首关于春天的短诗",
        model=models[0] if models else DEFAULT_MODEL,
        temperature=0.8,
        max_tokens=150
    )
    
    generate_response = client.generate(generate_request)
    
    if generate_response.status == 'success':
        print(f"模型: {generate_response.model}")
        print(f"生成内容: {generate_response.response}")
    else:
        print(f"生成失败: {generate_response.error}")

if __name__ == "__main__":
    main()
