"""
业务逻辑串联和处理
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

from client import OllamaClient, ChatRequest, ChatResponse

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaskType(Enum):
    """任务类型枚举"""
    CHAT = "chat"
    SUMMARIZE = "summarize"
    TRANSLATE = "translate"
    CODE_REVIEW = "code_review"
    CREATIVE_WRITING = "creative_writing"

@dataclass
class BusinessTask:
    """业务任务数据类"""
    task_id: str
    task_type: TaskType
    input_text: str
    model: str
    temperature: float = 0.7
    max_tokens: int = 1000
    context: Optional[Dict[str, Any]] = None

@dataclass
class BusinessResult:
    """业务结果数据类"""
    task_id: str
    task_type: TaskType
    success: bool
    result: str
    model: str
    execution_time: float
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class BusinessLogicProcessor:
    """业务逻辑处理器"""
    
    def __init__(self, client: OllamaClient):
        self.client = client
        self.task_templates = {
            TaskType.CHAT: "请回答以下问题：{input_text}",
            TaskType.SUMMARIZE: "请总结以下内容：\n\n{input_text}",
            TaskType.TRANSLATE: "请将以下内容翻译成中文：\n\n{input_text}",
            TaskType.CODE_REVIEW: "请对以下代码进行审查，指出潜在问题和改进建议：\n\n```\n{input_text}\n```",
            TaskType.CREATIVE_WRITING: "请根据以下主题进行创作：{input_text}"
        }
    
    def _prepare_prompt(self, task: BusinessTask) -> str:
        """准备提示词"""
        template = self.task_templates.get(task.task_type, "{input_text}")
        
        # 添加上下文信息
        if task.context:
            context_str = "\n".join([f"{k}: {v}" for k, v in task.context.items()])
            template = f"上下文信息：\n{context_str}\n\n" + template
        
        return template.format(input_text=task.input_text)
    
    def process_single_task(self, task: BusinessTask) -> BusinessResult:
        """处理单个业务任务"""
        start_time = time.time()
        
        try:
            logger.info(f"Processing task {task.task_id} of type {task.task_type.value}")
            
            # 准备提示词
            prompt = self._prepare_prompt(task)
            
            # 创建聊天请求
            chat_request = ChatRequest(
                message=prompt,
                model=task.model,
                temperature=task.temperature,
                max_tokens=task.max_tokens
            )
            
            # 发送请求
            response = self.client.chat(chat_request)
            
            execution_time = time.time() - start_time
            
            if response.status == 'success':
                logger.info(f"Task {task.task_id} completed successfully in {execution_time:.2f}s")
                
                return BusinessResult(
                    task_id=task.task_id,
                    task_type=task.task_type,
                    success=True,
                    result=response.response,
                    model=response.model,
                    execution_time=execution_time,
                    metadata={
                        'prompt_length': len(prompt),
                        'response_length': len(response.response)
                    }
                )
            else:
                logger.error(f"Task {task.task_id} failed: {response.error}")
                
                return BusinessResult(
                    task_id=task.task_id,
                    task_type=task.task_type,
                    success=False,
                    result="",
                    model=task.model,
                    execution_time=execution_time,
                    error=response.error
                )
                
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"Unexpected error processing task {task.task_id}: {str(e)}"
            logger.error(error_msg)
            
            return BusinessResult(
                task_id=task.task_id,
                task_type=task.task_type,
                success=False,
                result="",
                model=task.model,
                execution_time=execution_time,
                error=error_msg
            )
    
    def process_batch_tasks(self, tasks: List[BusinessTask]) -> List[BusinessResult]:
        """批量处理业务任务"""
        logger.info(f"Processing batch of {len(tasks)} tasks")
        
        results = []
        for task in tasks:
            result = self.process_single_task(task)
            results.append(result)
        
        # 统计结果
        successful_tasks = sum(1 for r in results if r.success)
        total_time = sum(r.execution_time for r in results)
        
        logger.info(f"Batch processing completed: {successful_tasks}/{len(tasks)} successful, "
                   f"total time: {total_time:.2f}s")
        
        return results
    
    def create_conversation_chain(self, messages: List[str], model: str) -> List[BusinessResult]:
        """创建对话链，每个消息都基于前面的上下文"""
        results = []
        conversation_history = []
        
        for i, message in enumerate(messages):
            # 构建上下文
            context = {}
            if conversation_history:
                context['conversation_history'] = "\n".join([
                    f"用户: {msg}" if j % 2 == 0 else f"助手: {msg}"
                    for j, msg in enumerate(conversation_history)
                ])
            
            # 创建任务
            task = BusinessTask(
                task_id=f"conversation_{i+1}",
                task_type=TaskType.CHAT,
                input_text=message,
                model=model,
                context=context
            )
            
            # 处理任务
            result = self.process_single_task(task)
            results.append(result)
            
            # 更新对话历史
            conversation_history.append(message)
            if result.success:
                conversation_history.append(result.result)
        
        return results

def create_demo_tasks() -> List[BusinessTask]:
    """创建演示任务"""
    return [
        BusinessTask(
            task_id="demo_chat",
            task_type=TaskType.CHAT,
            input_text="什么是人工智能？请简单解释一下。",
            model="llama2",
            temperature=0.7,
            max_tokens=200
        ),
        BusinessTask(
            task_id="demo_summarize",
            task_type=TaskType.SUMMARIZE,
            input_text="人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。这些任务包括学习、推理、问题解决、感知和语言理解。AI系统可以分为两大类：窄AI（专门针对特定任务）和通用AI（具有人类水平的认知能力）。目前，我们主要使用的是窄AI系统。",
            model="llama2",
            temperature=0.5,
            max_tokens=150
        ),
        BusinessTask(
            task_id="demo_creative",
            task_type=TaskType.CREATIVE_WRITING,
            input_text="未来的智能城市",
            model="llama2",
            temperature=0.9,
            max_tokens=300
        )
    ]

def main():
    """主函数演示业务逻辑"""
    print("=== Ollama 业务逻辑处理演示 ===\n")
    
    # 初始化客户端和处理器
    client = OllamaClient()
    processor = BusinessLogicProcessor(client)
    
    # 1. 健康检查
    print("1. 检查服务状态...")
    health = client.health_check()
    if health.get('status') != 'success':
        print(f"服务不可用: {health.get('error')}")
        return
    
    # 2. 获取可用模型
    models = client.get_models()
    if not models:
        print("没有可用的模型")
        return
    
    print(f"可用模型: {', '.join(models)}\n")
    
    # 3. 单任务处理演示
    print("2. 单任务处理演示...")
    single_task = BusinessTask(
        task_id="single_demo",
        task_type=TaskType.CHAT,
        input_text="请用一句话解释什么是机器学习",
        model=models[0]
    )
    
    single_result = processor.process_single_task(single_task)
    print(f"任务ID: {single_result.task_id}")
    print(f"成功: {single_result.success}")
    print(f"执行时间: {single_result.execution_time:.2f}s")
    if single_result.success:
        print(f"结果: {single_result.result}")
    else:
        print(f"错误: {single_result.error}")
    print()
    
    # 4. 批量任务处理演示
    print("3. 批量任务处理演示...")
    demo_tasks = create_demo_tasks()
    # 使用第一个可用模型
    for task in demo_tasks:
        task.model = models[0]
    
    batch_results = processor.process_batch_tasks(demo_tasks)
    
    for result in batch_results:
        print(f"任务 {result.task_id} ({result.task_type.value}):")
        print(f"  成功: {result.success}")
        print(f"  执行时间: {result.execution_time:.2f}s")
        if result.success:
            print(f"  结果: {result.result[:100]}...")
        else:
            print(f"  错误: {result.error}")
        print()
    
    # 5. 对话链演示
    print("4. 对话链演示...")
    conversation_messages = [
        "你好，我想了解Python编程",
        "Python有哪些主要特点？",
        "能给我一个简单的Python代码示例吗？"
    ]
    
    conversation_results = processor.create_conversation_chain(conversation_messages, models[0])
    
    for i, result in enumerate(conversation_results):
        print(f"对话轮次 {i+1}:")
        print(f"  用户: {conversation_messages[i]}")
        if result.success:
            print(f"  助手: {result.result}")
        else:
            print(f"  错误: {result.error}")
        print()

if __name__ == "__main__":
    main()
