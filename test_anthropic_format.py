#!/usr/bin/env python3
"""
测试ANTHROPIC格式兼容性
"""

import os
import requests
import json

def test_anthropic_format():
    """测试ANTHROPIC格式的环境变量和API调用"""
    
    # 设置ANTHROPIC格式的环境变量
    os.environ["ANTHROPIC_BASE_URL"] = "http://127.0.0.1:8001"
    os.environ["ANTHROPIC_AUTH_TOKEN"] = "ollama-qofpBPKHark0yGUSJ4E_wy3esdfulc5j"
    
    base_url = os.getenv("ANTHROPIC_BASE_URL")
    auth_token = os.getenv("ANTHROPIC_AUTH_TOKEN")
    
    print("🧪 测试ANTHROPIC格式兼容性")
    print("=" * 50)
    print(f"ANTHROPIC_BASE_URL: {base_url}")
    print(f"ANTHROPIC_AUTH_TOKEN: {auth_token[:10]}...{auth_token[-4:]}")
    print()
    
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "Content-Type": "application/json"
    }
    
    # 测试1: 获取模型列表
    print("1. 测试模型列表 (GET /v1/models)")
    try:
        response = requests.get(f"{base_url}/v1/models", headers=headers, timeout=10)
        if response.status_code == 200:
            models = response.json()
            print(f"   ✅ 成功: 找到 {len(models.get('data', []))} 个模型")
            for model in models.get('data', [])[:3]:
                print(f"      - {model['id']}")
        else:
            print(f"   ❌ 失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 异常: {str(e)}")
        return False
    
    print()
    
    # 测试2: 发送消息
    print("2. 测试消息发送 (POST /v1/messages)")
    try:
        data = {
            "model": "gemma3:1b",
            "max_tokens": 50,
            "messages": [
                {"role": "user", "content": "Please respond with exactly: 'ANTHROPIC format test successful!'"}
            ]
        }
        
        response = requests.post(f"{base_url}/v1/messages", json=data, headers=headers, timeout=30)
        if response.status_code == 200:
            result = response.json()
            content = result.get("content", [{}])[0].get("text", "")
            print(f"   ✅ 成功: {content[:60]}...")
            print(f"   📊 使用情况: 输入 {result.get('usage', {}).get('input_tokens', 0)} tokens, 输出 {result.get('usage', {}).get('output_tokens', 0)} tokens")
        else:
            print(f"   ❌ 失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 异常: {str(e)}")
        return False
    
    print()
    
    # 测试3: 使用Python客户端
    print("3. 测试Python客户端自动检测")
    try:
        from client import OllamaClient, ChatRequest
        
        client = OllamaClient()  # 应该自动使用ANTHROPIC环境变量
        print(f"   🌐 客户端URL: {client.base_url}")
        print(f"   🔑 客户端API Key: {client.api_key[:10]}...{client.api_key[-4:] if client.api_key else 'None'}")
        
        # 测试健康检查
        health = client.health_check()
        if health.get('status') == 'running':
            print("   ✅ 健康检查通过")
        else:
            print(f"   ⚠️ 健康检查: {health}")
        
        # 测试获取模型
        models = client.get_models()
        if models:
            print(f"   ✅ 获取到 {len(models)} 个模型: {', '.join(models)}")
        else:
            print("   ❌ 未获取到模型")
            return False
            
    except Exception as e:
        print(f"   ❌ Python客户端测试失败: {str(e)}")
        return False
    
    print()
    print("🎉 所有测试通过！ANTHROPIC格式完全兼容！")
    print()
    print("📋 您现在可以使用以下环境变量:")
    print(f"   export ANTHROPIC_BASE_URL={base_url}")
    print(f"   export ANTHROPIC_AUTH_TOKEN={auth_token}")
    print()
    print("🌐 就像使用其他ANTHROPIC兼容服务一样！")
    
    return True

if __name__ == "__main__":
    success = test_anthropic_format()
    if not success:
        print("\n❌ 测试失败，请检查服务是否正在运行")
        print("   启动命令: python3 start_anthropic_compatible.py")
        exit(1)
