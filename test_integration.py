#!/usr/bin/env python3
"""
集成测试脚本
"""

import time
import unittest
import requests
from typing import List

from client import OllamaClient, ChatRequest
from business_logic import BusinessLogicProcessor, BusinessTask, TaskType

class TestOllamaIntegration(unittest.TestCase):
    """Ollama集成测试"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.client = OllamaClient()
        cls.processor = BusinessLogicProcessor(cls.client)
        
        # 等待服务启动
        cls._wait_for_service()
        
        # 获取可用模型
        cls.available_models = cls.client.get_models()
        if not cls.available_models:
            raise unittest.SkipTest("没有可用的模型")
        
        cls.test_model = cls.available_models[0]
    
    @classmethod
    def _wait_for_service(cls, max_wait=30):
        """等待服务启动"""
        for _ in range(max_wait):
            try:
                response = requests.get("http://127.0.0.1:8000/health", timeout=2)
                if response.status_code == 200:
                    return
            except requests.exceptions.RequestException:
                pass
            time.sleep(1)
        
        raise unittest.SkipTest("API服务未启动")
    
    def test_health_check(self):
        """测试健康检查"""
        result = self.client.health_check()
        self.assertEqual(result.get('status'), 'success')
        self.assertIn('message', result)
    
    def test_get_models(self):
        """测试获取模型列表"""
        models = self.client.get_models()
        self.assertIsInstance(models, list)
        self.assertGreater(len(models), 0)
    
    def test_simple_chat(self):
        """测试简单聊天"""
        request = ChatRequest(
            message="Hello, please respond with 'Test successful'",
            model=self.test_model,
            max_tokens=50
        )
        
        response = self.client.chat(request)
        self.assertEqual(response.status, 'success')
        self.assertIsInstance(response.response, str)
        self.assertGreater(len(response.response), 0)
    
    def test_text_generation(self):
        """测试文本生成"""
        request = ChatRequest(
            message="Write a short sentence about AI",
            model=self.test_model,
            max_tokens=100
        )
        
        response = self.client.generate(request)
        self.assertEqual(response.status, 'success')
        self.assertIsInstance(response.response, str)
        self.assertGreater(len(response.response), 0)
    
    def test_business_single_task(self):
        """测试单个业务任务"""
        task = BusinessTask(
            task_id="test_task",
            task_type=TaskType.CHAT,
            input_text="What is 2+2?",
            model=self.test_model,
            max_tokens=50
        )
        
        result = self.processor.process_single_task(task)
        self.assertTrue(result.success)
        self.assertEqual(result.task_id, "test_task")
        self.assertGreater(len(result.result), 0)
        self.assertGreater(result.execution_time, 0)
    
    def test_business_batch_tasks(self):
        """测试批量业务任务"""
        tasks = [
            BusinessTask(
                task_id=f"batch_task_{i}",
                task_type=TaskType.CHAT,
                input_text=f"Count to {i+1}",
                model=self.test_model,
                max_tokens=30
            )
            for i in range(3)
        ]
        
        results = self.processor.process_batch_tasks(tasks)
        self.assertEqual(len(results), 3)
        
        for i, result in enumerate(results):
            self.assertEqual(result.task_id, f"batch_task_{i}")
            self.assertTrue(result.success)
            self.assertGreater(len(result.result), 0)
    
    def test_conversation_chain(self):
        """测试对话链"""
        messages = [
            "Hello",
            "What is your name?",
            "Thank you"
        ]
        
        results = self.processor.create_conversation_chain(messages, self.test_model)
        self.assertEqual(len(results), 3)
        
        for result in results:
            self.assertTrue(result.success)
            self.assertGreater(len(result.result), 0)
    
    def test_different_task_types(self):
        """测试不同任务类型"""
        test_cases = [
            (TaskType.CHAT, "Hello, how are you?"),
            (TaskType.SUMMARIZE, "This is a long text that needs to be summarized. It contains multiple sentences and ideas."),
            (TaskType.CREATIVE_WRITING, "A story about a robot"),
        ]
        
        for task_type, input_text in test_cases:
            with self.subTest(task_type=task_type):
                task = BusinessTask(
                    task_id=f"test_{task_type.value}",
                    task_type=task_type,
                    input_text=input_text,
                    model=self.test_model,
                    max_tokens=100
                )
                
                result = self.processor.process_single_task(task)
                self.assertTrue(result.success, f"Task {task_type.value} failed: {result.error}")
                self.assertGreater(len(result.result), 0)
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效模型
        request = ChatRequest(
            message="Test message",
            model="nonexistent_model",
            max_tokens=50
        )
        
        response = self.client.chat(request)
        self.assertEqual(response.status, 'error')
        self.assertIsNotNone(response.error)
    
    def test_parameter_variations(self):
        """测试不同参数设置"""
        base_request = ChatRequest(
            message="Write a short response",
            model=self.test_model
        )
        
        # 测试不同温度设置
        for temperature in [0.1, 0.5, 0.9]:
            with self.subTest(temperature=temperature):
                request = ChatRequest(
                    message=base_request.message,
                    model=base_request.model,
                    temperature=temperature,
                    max_tokens=50
                )
                
                response = self.client.chat(request)
                self.assertEqual(response.status, 'success')
        
        # 测试不同max_tokens设置
        for max_tokens in [10, 50, 100]:
            with self.subTest(max_tokens=max_tokens):
                request = ChatRequest(
                    message=base_request.message,
                    model=base_request.model,
                    max_tokens=max_tokens
                )
                
                response = self.client.chat(request)
                self.assertEqual(response.status, 'success')

def run_performance_test():
    """运行性能测试"""
    print("\n=== 性能测试 ===")
    
    client = OllamaClient()
    models = client.get_models()
    
    if not models:
        print("没有可用模型，跳过性能测试")
        return
    
    test_model = models[0]
    
    # 测试响应时间
    print("测试响应时间...")
    start_time = time.time()
    
    request = ChatRequest(
        message="Please respond with exactly 10 words.",
        model=test_model,
        max_tokens=20
    )
    
    response = client.chat(request)
    end_time = time.time()
    
    if response.status == 'success':
        print(f"响应时间: {end_time - start_time:.2f}秒")
        print(f"响应长度: {len(response.response)}字符")
    else:
        print(f"性能测试失败: {response.error}")
    
    # 测试并发处理（简单版本）
    print("\n测试批量处理...")
    processor = BusinessLogicProcessor(client)
    
    tasks = [
        BusinessTask(
            task_id=f"perf_task_{i}",
            task_type=TaskType.CHAT,
            input_text=f"Count from 1 to {i+1}",
            model=test_model,
            max_tokens=30
        )
        for i in range(5)
    ]
    
    batch_start = time.time()
    results = processor.process_batch_tasks(tasks)
    batch_end = time.time()
    
    successful_tasks = sum(1 for r in results if r.success)
    print(f"批量处理结果: {successful_tasks}/{len(tasks)}成功")
    print(f"总时间: {batch_end - batch_start:.2f}秒")
    print(f"平均每任务: {(batch_end - batch_start)/len(tasks):.2f}秒")

def main():
    """主函数"""
    print("=== Ollama 集成测试 ===")
    
    # 运行单元测试
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行性能测试
    run_performance_test()

if __name__ == "__main__":
    main()
