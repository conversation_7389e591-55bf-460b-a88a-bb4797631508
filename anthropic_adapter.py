#!/usr/bin/env python3
"""
ANTHROPIC API 兼容适配器
让Ollama服务兼容ANTHROPIC API格式的请求
"""

import json
import logging
import os
import sys
from typing import List, Optional, Dict, Any
from fastapi import FastAPI, HTTPException, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
import requests
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局配置
OLLAMA_API_URL = "http://127.0.0.1:8000"
CURRENT_API_KEY = ""

# ANTHROPIC模型到Ollama模型的映射
ANTHROPIC_TO_OLLAMA_MODEL_MAP = {
    # Claude 3.5 Sonnet 系列
    "claude-3-5-sonnet-20241022": "gemma3:1b",
    "claude-3-5-sonnet-20240620": "gemma3:1b",
    "claude-3-5-sonnet": "gemma3:1b",

    # Claude 3.5 Haiku 系列
    "claude-3-5-haiku-20241022": "gemma3:1b",
    "claude-3-5-haiku": "gemma3:1b",

    # Claude 3 系列
    "claude-3-opus-20240229": "gemma3:1b",
    "claude-3-opus": "gemma3:1b",
    "claude-3-sonnet-20240229": "gemma3:1b",
    "claude-3-sonnet": "gemma3:1b",
    "claude-3-haiku-20240307": "gemma3:1b",
    "claude-3-haiku": "gemma3:1b",

    # Claude 2 系列
    "claude-2.1": "gemma3:1b",
    "claude-2.0": "gemma3:1b",
    "claude-2": "gemma3:1b",

    # Claude Instant 系列
    "claude-instant-1.2": "gemma3:1b",
    "claude-instant-1": "gemma3:1b",
    "claude-instant": "gemma3:1b",

    # 新的Claude 4系列 (假设的未来模型)
    "claude-4": "gemma3:1b",
    "claude-sonnet-4": "gemma3:1b",
    "claude-sonnet-4-20250514": "gemma3:1b",

    # 通用映射
    "claude": "gemma3:1b",
}

# 可用的Ollama模型缓存
AVAILABLE_OLLAMA_MODELS = []

# 创建FastAPI应用
app = FastAPI(
    title="Anthropic-Compatible Ollama API",
    description="ANTHROPIC API兼容的Ollama本地大语言模型服务",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API Key认证
security = HTTPBearer(auto_error=False)

def verify_api_key(request: Request, credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)):
    """验证API Key - 兼容ANTHROPIC格式"""
    global CURRENT_API_KEY
    
    if not CURRENT_API_KEY:
        return True
    
    # 检查Authorization header (Bearer token) - ANTHROPIC标准格式
    if credentials and credentials.credentials == CURRENT_API_KEY:
        return True
    
    # 检查x-api-key header - ANTHROPIC格式
    api_key = request.headers.get("x-api-key")
    if api_key == CURRENT_API_KEY:
        return True
    
    # 检查X-API-Key header - 我们的格式
    api_key = request.headers.get("X-API-Key")
    if api_key == CURRENT_API_KEY:
        return True
    
    raise HTTPException(
        status_code=401,
        detail={"error": {"type": "authentication_error", "message": "Invalid API key"}}
    )

# ANTHROPIC API兼容的数据模型
from typing import Union

class AnthropicMessage(BaseModel):
    role: str
    content: Union[str, List[Dict[str, Any]]]  # 支持字符串或数组格式

class AnthropicRequest(BaseModel):
    model: str
    max_tokens: int
    messages: List[AnthropicMessage]
    temperature: Optional[float] = 0.7
    system: Optional[Union[str, List[Dict[str, Any]]]] = None  # 支持字符串或数组格式
    # 添加更多ANTHROPIC API支持的字段
    top_p: Optional[float] = None
    top_k: Optional[int] = None
    stop_sequences: Optional[List[str]] = None
    stream: Optional[bool] = False
    metadata: Optional[Dict[str, Any]] = None

class AnthropicResponse(BaseModel):
    id: str
    type: str = "message"
    role: str = "assistant"
    content: List[Dict[str, Any]]
    model: str
    stop_reason: str = "end_turn"
    stop_sequence: Optional[str] = None
    usage: Dict[str, int]

def get_available_ollama_models() -> List[str]:
    """获取可用的Ollama模型列表"""
    global AVAILABLE_OLLAMA_MODELS

    try:
        headers = {}
        if CURRENT_API_KEY:
            headers["X-API-Key"] = CURRENT_API_KEY

        response = requests.get(f"{OLLAMA_API_URL}/models", headers=headers, timeout=10)
        if response.status_code == 200:
            result = response.json()
            models = result.get("models", [])
            AVAILABLE_OLLAMA_MODELS = models
            logger.info(f"Available Ollama models: {models}")
            return models
        else:
            logger.error(f"Failed to get Ollama models: {response.status_code}")
            return AVAILABLE_OLLAMA_MODELS
    except Exception as e:
        logger.error(f"Error getting Ollama models: {str(e)}")
        return AVAILABLE_OLLAMA_MODELS

def map_anthropic_to_ollama_model(anthropic_model: str) -> str:
    """将ANTHROPIC模型名映射到Ollama模型名"""
    # 首先检查直接映射
    if anthropic_model in ANTHROPIC_TO_OLLAMA_MODEL_MAP:
        ollama_model = ANTHROPIC_TO_OLLAMA_MODEL_MAP[anthropic_model]
        logger.info(f"Model mapping: {anthropic_model} -> {ollama_model}")
        return ollama_model

    # 如果没有直接映射，尝试模糊匹配
    anthropic_lower = anthropic_model.lower()

    # 检查是否包含claude关键词
    if "claude" in anthropic_lower:
        # 获取可用模型
        available_models = get_available_ollama_models()
        if available_models:
            default_model = available_models[0]
            logger.info(f"Fuzzy mapping for Claude model {anthropic_model} -> {default_model}")
            return default_model

    # 检查是否已经是Ollama模型名
    available_models = get_available_ollama_models()
    if anthropic_model in available_models:
        logger.info(f"Model {anthropic_model} is already an Ollama model")
        return anthropic_model

    # 如果都不匹配，使用默认模型
    if available_models:
        default_model = available_models[0]
        logger.warning(f"Unknown model {anthropic_model}, using default: {default_model}")
        return default_model
    else:
        # 如果连默认模型都没有，使用硬编码的fallback
        fallback_model = "gemma3:1b"
        logger.error(f"No available models, using fallback: {fallback_model}")
        return fallback_model

def get_anthropic_model_list() -> List[Dict[str, Any]]:
    """获取ANTHROPIC格式的模型列表"""
    # 获取可用的Ollama模型
    ollama_models = get_available_ollama_models()

    # 创建ANTHROPIC格式的模型列表
    anthropic_models = []

    # 添加所有映射的ANTHROPIC模型
    for anthropic_name in ANTHROPIC_TO_OLLAMA_MODEL_MAP.keys():
        anthropic_models.append({
            "id": anthropic_name,
            "object": "model",
            "created": 1677610602,
            "owned_by": "anthropic"
        })

    # 也添加原始的Ollama模型（用于直接访问）
    for ollama_name in ollama_models:
        anthropic_models.append({
            "id": ollama_name,
            "object": "model",
            "created": 1677610602,
            "owned_by": "ollama"
        })

    return anthropic_models

def extract_content_text(content: Union[str, List[Dict[str, Any]], Dict[str, Any], Any]) -> str:
    """从ANTHROPIC格式的content中提取文本 - 支持多种格式"""
    if isinstance(content, str):
        return content
    elif isinstance(content, list):
        # 提取所有text类型的内容
        text_parts = []
        for item in content:
            if isinstance(item, dict):
                # 标准的ANTHROPIC格式: {"type": "text", "text": "..."}
                if item.get("type") == "text" and "text" in item:
                    text_parts.append(item["text"])
                # 可能的嵌套格式: {"content": "..."}
                elif "content" in item:
                    nested_content = extract_content_text(item["content"])
                    text_parts.append(nested_content)
                # 直接包含text字段
                elif "text" in item:
                    text_parts.append(item["text"])
                # 可能的message格式
                elif "message" in item:
                    text_parts.append(str(item["message"]))
                # 其他可能的格式，尝试找到文本内容
                else:
                    # 查找可能的文本字段
                    for key in ["value", "data", "body"]:
                        if key in item:
                            text_parts.append(str(item[key]))
                            break
                    else:
                        # 如果没有找到明确的文本字段，转换整个对象
                        text_parts.append(str(item))
            elif isinstance(item, str):
                text_parts.append(item)
            else:
                text_parts.append(str(item))
        return " ".join(text_parts)
    elif isinstance(content, dict):
        # 处理单个字典对象
        if content.get("type") == "text" and "text" in content:
            return content["text"]
        elif "content" in content:
            return extract_content_text(content["content"])
        elif "text" in content:
            return content["text"]
        elif "message" in content:
            return str(content["message"])
        else:
            # 查找可能的文本字段
            for key in ["value", "data", "body"]:
                if key in content:
                    return str(content[key])
            # 如果没有找到，转换整个对象
            return str(content)
    else:
        return str(content)

@app.get("/")
async def root():
    """根路径"""
    return {
        "service": "Anthropic-Compatible Ollama API",
        "status": "running",
        "api_key_required": bool(CURRENT_API_KEY)
    }

@app.post("/v1/messages")
async def create_message(request: AnthropicRequest, authenticated: bool = Depends(verify_api_key)):
    """ANTHROPIC兼容的消息创建接口"""
    try:
        logger.info(f"Received ANTHROPIC request: model={request.model}, messages={len(request.messages)}, system={'yes' if request.system else 'no'}")

        # 映射ANTHROPIC模型到Ollama模型
        ollama_model = map_anthropic_to_ollama_model(request.model)
        logger.info(f"Using Ollama model: {ollama_model} (mapped from {request.model})")

        # 转换ANTHROPIC格式到Ollama格式
        if not request.messages:
            raise HTTPException(status_code=400, detail={"error": {"type": "invalid_request_error", "message": "Messages cannot be empty"}})
        
        # 构建完整的对话上下文
        conversation_parts = []

        # 添加system消息（如果有），处理可能是数组的情况
        if request.system:
            try:
                system_text = extract_content_text(request.system)
                conversation_parts.append(f"System: {system_text}")
                logger.info(f"Processed system message: {system_text[:100]}...")
            except Exception as e:
                logger.error(f"Error processing system message: {str(e)}")
                # 继续处理，不因为system消息失败而中断

        # 处理所有消息，构建对话历史
        for i, msg in enumerate(request.messages):
            try:
                content_text = extract_content_text(msg.content)
                if msg.role == "user":
                    conversation_parts.append(f"User: {content_text}")
                elif msg.role == "assistant":
                    conversation_parts.append(f"Assistant: {content_text}")
                elif msg.role == "system":
                    conversation_parts.append(f"System: {content_text}")
                logger.debug(f"Processed message {i}: {msg.role} - {content_text[:50]}...")
            except Exception as e:
                logger.error(f"Error processing message {i}: {str(e)}")
                # 尝试使用原始内容
                conversation_parts.append(f"{msg.role.title()}: {str(msg.content)}")

        # 组合成完整的消息
        user_message = "\n\n".join(conversation_parts)

        if not user_message.strip():
            raise HTTPException(status_code=400, detail={"error": {"type": "invalid_request_error", "message": "No valid message content found"}})
        
        # 调用Ollama API，构建请求参数（使用映射后的模型）
        ollama_request = {
            "message": user_message,
            "model": ollama_model,  # 使用映射后的Ollama模型
            "temperature": request.temperature,
            "max_tokens": request.max_tokens
        }

        # 添加可选参数（如果Ollama支持）
        if request.top_p is not None:
            ollama_request["top_p"] = request.top_p
        if request.top_k is not None:
            ollama_request["top_k"] = request.top_k
        if request.stop_sequences:
            ollama_request["stop"] = request.stop_sequences
        
        headers = {}
        if CURRENT_API_KEY:
            headers["X-API-Key"] = CURRENT_API_KEY
        
        response = requests.post(
            f"{OLLAMA_API_URL}/chat",
            json=ollama_request,
            headers=headers,
            timeout=60
        )
        
        if response.status_code != 200:
            logger.error(f"Ollama API error: {response.status_code} - {response.text}")
            raise HTTPException(
                status_code=response.status_code,
                detail={"error": {"type": "api_error", "message": f"Ollama API error: {response.text}"}}
            )
        
        ollama_response = response.json()
        
        if ollama_response.get("status") != "success":
            raise HTTPException(
                status_code=500,
                detail={"error": {"type": "api_error", "message": ollama_response.get("error", "Unknown error")}}
            )
        
        # 转换为ANTHROPIC格式响应（返回原始的ANTHROPIC模型名）
        anthropic_response = AnthropicResponse(
            id=f"msg_{hash(user_message) % 1000000}",
            content=[{
                "type": "text",
                "text": ollama_response["response"]
            }],
            model=request.model,  # 返回原始的ANTHROPIC模型名，不是映射后的
            usage={
                "input_tokens": len(user_message.split()),
                "output_tokens": len(ollama_response["response"].split())
            }
        )

        logger.info(f"Response generated successfully for model {request.model} (actual: {ollama_model})")
        
        return anthropic_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"error": {"type": "api_error", "message": str(e)}}
        )

@app.get("/v1/models")
async def list_models(authenticated: bool = Depends(verify_api_key)):
    """获取可用模型列表 - ANTHROPIC兼容格式"""
    try:
        logger.info("Getting ANTHROPIC compatible model list")

        # 获取ANTHROPIC格式的模型列表（包含映射的模型）
        models = get_anthropic_model_list()

        logger.info(f"Returning {len(models)} models (ANTHROPIC + Ollama)")

        return {
            "object": "list",
            "data": models
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting models: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"error": {"type": "api_error", "message": str(e)}}
        )

def initialize_model_mapping():
    """初始化模型映射"""
    logger.info("Initializing model mapping...")

    # 获取可用的Ollama模型
    available_models = get_available_ollama_models()

    if not available_models:
        logger.warning("No Ollama models available!")
        return

    # 更新映射表，确保所有ANTHROPIC模型都映射到可用的Ollama模型
    default_model = available_models[0]

    # 动态更新映射表
    for anthropic_model in list(ANTHROPIC_TO_OLLAMA_MODEL_MAP.keys()):
        mapped_model = ANTHROPIC_TO_OLLAMA_MODEL_MAP[anthropic_model]
        if mapped_model not in available_models:
            ANTHROPIC_TO_OLLAMA_MODEL_MAP[anthropic_model] = default_model
            logger.info(f"Updated mapping: {anthropic_model} -> {default_model}")

    logger.info(f"Model mapping initialized. Available Ollama models: {available_models}")
    logger.info(f"Default model for ANTHROPIC requests: {default_model}")

def main():
    """主函数"""
    global CURRENT_API_KEY

    import argparse

    parser = argparse.ArgumentParser(description="Anthropic-Compatible Ollama API Server")
    parser.add_argument("--api-key", type=str, help="API Key for authentication")
    parser.add_argument("--host", type=str, default="127.0.0.1", help="Host to bind")
    parser.add_argument("--port", type=int, default=8001, help="Port to bind")
    parser.add_argument("--ollama-url", type=str, default="http://127.0.0.1:8000", help="Ollama API URL")

    args = parser.parse_args()
    
    # 设置API Key
    CURRENT_API_KEY = (
        args.api_key or
        os.getenv("ANTHROPIC_AUTH_TOKEN") or
        os.getenv("ANTHROPIC_API_KEY") or
        os.getenv("OLLAMA_AUTH_TOKEN") or
        os.getenv("OLLAMA_API_KEY") or
        ""
    )
    
    global OLLAMA_API_URL
    OLLAMA_API_URL = args.ollama_url
    
    print("🔗 Anthropic-Compatible Ollama API Server")
    print("=" * 50)
    print(f"🌐 Server: http://{args.host}:{args.port}")
    print(f"🔗 Ollama API: {OLLAMA_API_URL}")
    if CURRENT_API_KEY:
        print(f"🔑 API Key: {CURRENT_API_KEY[:10]}...{CURRENT_API_KEY[-4:]}")
    else:
        print("⚠️  No API Key - running without authentication")
    print()
    print("📋 ANTHROPIC Compatible Endpoints:")
    print(f"   POST http://{args.host}:{args.port}/v1/messages")
    print(f"   GET  http://{args.host}:{args.port}/v1/models")
    print()
    
    uvicorn.run(
        "anthropic_adapter:app",
        host=args.host,
        port=args.port,
        reload=False,
        log_level="info"
    )

if __name__ == "__main__":
    main()
