#!/usr/bin/env python3
"""
ANTHROPIC API 兼容适配器
让Ollama服务兼容ANTHROPIC API格式的请求
"""

import json
import logging
import os
import sys
from typing import List, Optional, Dict, Any
from fastapi import FastAPI, HTTPException, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
import requests
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局配置
OLLAMA_API_URL = "http://127.0.0.1:8000"
CURRENT_API_KEY = ""

# 创建FastAPI应用
app = FastAPI(
    title="Anthropic-Compatible Ollama API",
    description="ANTHROPIC API兼容的Ollama本地大语言模型服务",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API Key认证
security = HTTPBearer(auto_error=False)

def verify_api_key(request: Request, credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)):
    """验证API Key - 兼容ANTHROPIC格式"""
    global CURRENT_API_KEY
    
    if not CURRENT_API_KEY:
        return True
    
    # 检查Authorization header (Bearer token) - ANTHROPIC标准格式
    if credentials and credentials.credentials == CURRENT_API_KEY:
        return True
    
    # 检查x-api-key header - ANTHROPIC格式
    api_key = request.headers.get("x-api-key")
    if api_key == CURRENT_API_KEY:
        return True
    
    # 检查X-API-Key header - 我们的格式
    api_key = request.headers.get("X-API-Key")
    if api_key == CURRENT_API_KEY:
        return True
    
    raise HTTPException(
        status_code=401,
        detail={"error": {"type": "authentication_error", "message": "Invalid API key"}}
    )

# ANTHROPIC API兼容的数据模型
class AnthropicMessage(BaseModel):
    role: str
    content: str

class AnthropicRequest(BaseModel):
    model: str
    max_tokens: int
    messages: List[AnthropicMessage]
    temperature: Optional[float] = 0.7
    system: Optional[str] = None

class AnthropicResponse(BaseModel):
    id: str
    type: str = "message"
    role: str = "assistant"
    content: List[Dict[str, Any]]
    model: str
    stop_reason: str = "end_turn"
    stop_sequence: Optional[str] = None
    usage: Dict[str, int]

@app.get("/")
async def root():
    """根路径"""
    return {
        "service": "Anthropic-Compatible Ollama API",
        "status": "running",
        "api_key_required": bool(CURRENT_API_KEY)
    }

@app.post("/v1/messages")
async def create_message(request: AnthropicRequest, authenticated: bool = Depends(verify_api_key)):
    """ANTHROPIC兼容的消息创建接口"""
    try:
        # 转换ANTHROPIC格式到Ollama格式
        if not request.messages:
            raise HTTPException(status_code=400, detail={"error": {"type": "invalid_request_error", "message": "Messages cannot be empty"}})
        
        # 获取最后一条用户消息
        user_message = ""
        for msg in reversed(request.messages):
            if msg.role == "user":
                user_message = msg.content
                break
        
        if not user_message:
            raise HTTPException(status_code=400, detail={"error": {"type": "invalid_request_error", "message": "No user message found"}})
        
        # 如果有system消息，添加到用户消息前
        if request.system:
            user_message = f"System: {request.system}\n\nUser: {user_message}"
        
        # 调用Ollama API
        ollama_request = {
            "message": user_message,
            "model": request.model,
            "temperature": request.temperature,
            "max_tokens": request.max_tokens
        }
        
        headers = {}
        if CURRENT_API_KEY:
            headers["X-API-Key"] = CURRENT_API_KEY
        
        response = requests.post(
            f"{OLLAMA_API_URL}/chat",
            json=ollama_request,
            headers=headers,
            timeout=60
        )
        
        if response.status_code != 200:
            logger.error(f"Ollama API error: {response.status_code} - {response.text}")
            raise HTTPException(
                status_code=response.status_code,
                detail={"error": {"type": "api_error", "message": f"Ollama API error: {response.text}"}}
            )
        
        ollama_response = response.json()
        
        if ollama_response.get("status") != "success":
            raise HTTPException(
                status_code=500,
                detail={"error": {"type": "api_error", "message": ollama_response.get("error", "Unknown error")}}
            )
        
        # 转换为ANTHROPIC格式响应
        anthropic_response = AnthropicResponse(
            id=f"msg_{hash(user_message) % 1000000}",
            content=[{
                "type": "text",
                "text": ollama_response["response"]
            }],
            model=request.model,
            usage={
                "input_tokens": len(user_message.split()),
                "output_tokens": len(ollama_response["response"].split())
            }
        )
        
        return anthropic_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"error": {"type": "api_error", "message": str(e)}}
        )

@app.get("/v1/models")
async def list_models(authenticated: bool = Depends(verify_api_key)):
    """获取可用模型列表 - ANTHROPIC兼容格式"""
    try:
        headers = {}
        if CURRENT_API_KEY:
            headers["X-API-Key"] = CURRENT_API_KEY
        
        response = requests.get(f"{OLLAMA_API_URL}/models", headers=headers, timeout=10)
        
        if response.status_code != 200:
            raise HTTPException(
                status_code=response.status_code,
                detail={"error": {"type": "api_error", "message": "Failed to get models"}}
            )
        
        ollama_models = response.json()
        
        # 转换为ANTHROPIC格式
        models = []
        for model_name in ollama_models.get("models", []):
            models.append({
                "id": model_name,
                "object": "model",
                "created": 1677610602,
                "owned_by": "ollama"
            })
        
        return {
            "object": "list",
            "data": models
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting models: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"error": {"type": "api_error", "message": str(e)}}
        )

def main():
    """主函数"""
    global CURRENT_API_KEY
    
    import argparse
    
    parser = argparse.ArgumentParser(description="Anthropic-Compatible Ollama API Server")
    parser.add_argument("--api-key", type=str, help="API Key for authentication")
    parser.add_argument("--host", type=str, default="127.0.0.1", help="Host to bind")
    parser.add_argument("--port", type=int, default=8001, help="Port to bind")
    parser.add_argument("--ollama-url", type=str, default="http://127.0.0.1:8000", help="Ollama API URL")
    
    args = parser.parse_args()
    
    # 设置API Key
    CURRENT_API_KEY = (
        args.api_key or
        os.getenv("ANTHROPIC_AUTH_TOKEN") or
        os.getenv("ANTHROPIC_API_KEY") or
        os.getenv("OLLAMA_AUTH_TOKEN") or
        os.getenv("OLLAMA_API_KEY") or
        ""
    )
    
    global OLLAMA_API_URL
    OLLAMA_API_URL = args.ollama_url
    
    print("🔗 Anthropic-Compatible Ollama API Server")
    print("=" * 50)
    print(f"🌐 Server: http://{args.host}:{args.port}")
    print(f"🔗 Ollama API: {OLLAMA_API_URL}")
    if CURRENT_API_KEY:
        print(f"🔑 API Key: {CURRENT_API_KEY[:10]}...{CURRENT_API_KEY[-4:]}")
    else:
        print("⚠️  No API Key - running without authentication")
    print()
    print("📋 ANTHROPIC Compatible Endpoints:")
    print(f"   POST http://{args.host}:{args.port}/v1/messages")
    print(f"   GET  http://{args.host}:{args.port}/v1/models")
    print()
    
    uvicorn.run(
        "anthropic_adapter:app",
        host=args.host,
        port=args.port,
        reload=False,
        log_level="info"
    )

if __name__ == "__main__":
    main()
