# 🚀 Ollama API 服务使用指南

## 📋 完整启动命令

### 1. 生成API Key

```bash
# 生成随机API Key
python start_with_key.py --generate-key
```

输出示例：
```
Generated API Key: ollama-Kx9mP2vQ8nR7sL4tY6uI3oE1wZ5cA9bF
```

### 2. 启动服务（完整命令）

```bash
# 使用生成的API Key启动服务
python start_with_key.py --api-key ollama-Kx9mP2vQ8nR7sL4tY6uI3oE1wZ5cA9bF --host 127.0.0.1 --port 8000
```

### 3. 使用环境变量（推荐）

```bash
# 设置环境变量
export OLLAMA_API_KEY=ollama-Kx9mP2vQ8nR7sL4tY6uI3oE1wZ5cA9bF

# 启动服务
python start_with_key.py
```

## 🔧 所有可用的启动选项

### start_with_key.py 参数

```bash
python start_with_key.py [OPTIONS]

选项:
  --api-key TEXT          API Key for authentication
  --generate-key          Generate a new API key
  --host TEXT            Host to bind the server (default: 127.0.0.1)
  --port INTEGER         Port to bind the server (default: 8000)
  --no-reload            Disable auto-reload
  --test-only            Only run tests, don't start server
  --skip-checks          Skip dependency and Ollama checks
  --help                 Show this message and exit
```

### api_server.py 参数

```bash
python api_server.py [OPTIONS]

选项:
  --api-key TEXT         API Key for authentication
  --host TEXT           Host to bind the server (default: 127.0.0.1)
  --port INTEGER        Port to bind the server (default: 8000)
  --no-reload           Disable auto-reload in development mode
  --generate-key        Generate a random API key and exit
  --help                Show this message and exit
```

## 🎯 常用启动场景

### 场景1：开发环境快速启动

```bash
# 1. 生成API Key
python start_with_key.py --generate-key

# 2. 复制生成的Key，然后启动
python start_with_key.py --api-key YOUR_GENERATED_KEY
```

### 场景2：生产环境启动

```bash
# 1. 设置环境变量
export OLLAMA_API_KEY=your-secure-api-key-here

# 2. 启动服务（禁用自动重载）
python start_with_key.py --no-reload --host 0.0.0.0 --port 8000
```

### 场景3：测试环境

```bash
# 使用默认演示Key启动
python start_with_key.py --api-key ollama-demo-key-2024
```

### 场景4：仅测试API

```bash
# 假设服务已在运行，仅测试API调用
export OLLAMA_API_KEY=your-api-key-here
python start_with_key.py --test-only
```

## 🧪 客户端测试命令

### 设置API Key环境变量

```bash
export OLLAMA_API_KEY=your-api-key-here
```

### 运行各种测试

```bash
# 基础客户端测试
python client.py

# 业务逻辑测试
python business_logic.py

# 完整功能演示
python demo.py

# 集成测试
python test_integration.py
```

## 🌐 HTTP API 调用示例

### 使用curl测试

```bash
# 设置API Key变量
API_KEY="your-api-key-here"

# 健康检查（无需认证）
curl http://127.0.0.1:8000/health

# 获取模型列表
curl -H "X-API-Key: $API_KEY" http://127.0.0.1:8000/models

# 发送聊天请求
curl -X POST "http://127.0.0.1:8000/chat" \
     -H "Content-Type: application/json" \
     -H "X-API-Key: $API_KEY" \
     -d '{
       "message": "Hello, how are you?",
       "model": "llama2",
       "temperature": 0.7,
       "max_tokens": 100
     }'
```

### 使用Python requests

```python
import requests

API_KEY = "your-api-key-here"
BASE_URL = "http://127.0.0.1:8000"

headers = {
    "X-API-Key": API_KEY,
    "Content-Type": "application/json"
}

# 获取模型列表
response = requests.get(f"{BASE_URL}/models", headers=headers)
print(response.json())

# 发送聊天请求
chat_data = {
    "message": "What is artificial intelligence?",
    "model": "llama2",
    "temperature": 0.7,
    "max_tokens": 200
}

response = requests.post(f"{BASE_URL}/chat", json=chat_data, headers=headers)
print(response.json())
```

## 🔍 故障排除

### 1. 检查Ollama服务

```bash
# 检查Ollama是否运行
curl http://127.0.0.1:11434/api/tags

# 如果没有响应，启动Ollama
ollama serve
```

### 2. 检查模型

```bash
# 列出已下载的模型
ollama list

# 下载模型（如果没有）
ollama pull llama2
```

### 3. 检查API服务

```bash
# 检查API服务健康状态
curl http://127.0.0.1:8000/health
```

### 4. 验证API Key

```bash
# 测试API Key是否有效
curl -H "X-API-Key: your-api-key-here" http://127.0.0.1:8000/models
```

## 📝 完整工作流程示例

```bash
# 1. 确保Ollama运行
ollama serve &

# 2. 下载模型
ollama pull llama2

# 3. 生成API Key
python start_with_key.py --generate-key
# 输出: Generated API Key: ollama-abc123...

# 4. 设置环境变量
export OLLAMA_API_KEY=ollama-abc123...

# 5. 启动API服务
python start_with_key.py &

# 6. 等待服务启动
sleep 5

# 7. 测试API
python client.py

# 8. 运行完整演示
python demo.py
```

## 🔐 安全最佳实践

1. **不要在代码中硬编码API Key**
2. **使用环境变量存储API Key**
3. **定期更换API Key**
4. **生产环境使用HTTPS**
5. **限制API访问IP范围**

## 📚 更多资源

- API文档: http://127.0.0.1:8000/docs
- ReDoc文档: http://127.0.0.1:8000/redoc
- 健康检查: http://127.0.0.1:8000/health
- 项目README: README.md
