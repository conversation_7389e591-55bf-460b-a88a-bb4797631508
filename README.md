# Ollama 本地大语言模型 API 服务

这个项目提供了一个完整的解决方案，用于在本地部署Ollama大语言模型并通过HTTP API进行调用。包含FastAPI服务封装、HTTP客户端调用逻辑和业务流程串联。

## 项目结构

```
.
├── requirements.txt          # Python依赖包
├── config.py                # 配置文件
├── api_server.py            # FastAPI服务器，封装Ollama接口
├── client.py                # HTTP客户端调用逻辑
├── business_logic.py        # 业务逻辑串联和处理
├── start_server.py          # 启动脚本（推荐使用）
├── test_integration.py      # 集成测试脚本
└── README.md                # 项目说明
```

## 快速开始

### 方法一：使用带API Key的启动脚本（推荐）

```bash
# 生成API Key
python start_with_key.py --generate-key

# 使用API Key启动服务
python start_with_key.py --api-key your-api-key-here

# 或者使用环境变量
export OLLAMA_API_KEY=your-api-key-here
python start_with_key.py
```

### 方法二：使用传统启动脚本

```bash
# 一键启动（自动检查依赖和服务，但无API Key认证）
python start_server.py
```

### 方法三：手动启动

#### 1. 安装依赖

```bash
pip install -r requirements.txt
```

#### 2. 安装和启动Ollama

```bash
# 安装Ollama (macOS/Linux)
curl -fsSL https://ollama.ai/install.sh | sh

# Windows用户请访问 https://ollama.ai 下载安装包

# 下载模型 (例如llama2, qwen, gemma等)
ollama pull llama2
ollama pull qwen:7b

# 启动Ollama服务
ollama serve
```

#### 3. 启动API服务（带API Key认证）

```bash
# 生成API Key
python api_server.py --generate-key

# 使用API Key启动
python api_server.py --api-key your-api-key-here

# 或使用环境变量
export OLLAMA_API_KEY=your-api-key-here
python api_server.py
```

#### 4. 测试服务

```bash
# 设置API Key环境变量
export OLLAMA_API_KEY=your-api-key-here

# 客户端测试
python client.py

# 业务逻辑测试
python business_logic.py

# 完整演示
python demo.py

# 集成测试
python test_integration.py
```

## API接口文档

服务启动后，可以访问以下地址查看完整API文档：
- Swagger UI: http://127.0.0.1:8000/docs
- ReDoc: http://127.0.0.1:8000/redoc

### 主要接口

#### GET /health
- 功能：健康检查
- 响应：`{"status": "success", "message": "服务状态信息"}`

#### GET /models
- 功能：获取可用模型列表
- 响应：`{"models": ["llama2", "qwen:7b"], "status": "success"}`

#### POST /chat
- 功能：与大语言模型对话
- 请求：
  ```json
  {
    "message": "你好，请介绍一下你自己",
    "model": "llama2",
    "temperature": 0.7,
    "max_tokens": 1000
  }
  ```
- 响应：
  ```json
  {
    "response": "模型的回答内容",
    "status": "success",
    "model": "llama2"
  }
  ```

#### POST /generate
- 功能：文本生成
- 请求格式同 `/chat`
- 响应格式同 `/chat`

## 使用示例

### Python客户端调用示例

```python
from client import OllamaClient, ChatRequest

# 创建客户端
client = OllamaClient()

# 简单对话
request = ChatRequest(
    message="什么是人工智能？",
    model="llama2",
    temperature=0.7,
    max_tokens=200
)

response = client.chat(request)
if response.status == 'success':
    print(f"回答: {response.response}")
else:
    print(f"错误: {response.error}")
```

### 业务逻辑处理示例

```python
from business_logic import BusinessLogicProcessor, BusinessTask, TaskType
from client import OllamaClient

# 创建处理器
client = OllamaClient()
processor = BusinessLogicProcessor(client)

# 创建业务任务
task = BusinessTask(
    task_id="demo_task",
    task_type=TaskType.SUMMARIZE,
    input_text="这里是需要总结的长文本内容...",
    model="llama2"
)

# 处理任务
result = processor.process_single_task(task)
print(f"处理结果: {result.result}")
```

### HTTP直接调用示例

```bash
# 健康检查（无需认证）
curl http://127.0.0.1:8000/health

# 获取模型列表（需要API Key）
curl -H "X-API-Key: your-api-key-here" http://127.0.0.1:8000/models

# 使用Bearer Token认证
curl -H "Authorization: Bearer your-api-key-here" http://127.0.0.1:8000/models

# 发送聊天请求（需要API Key）
curl -X POST "http://127.0.0.1:8000/chat" \
     -H "Content-Type: application/json" \
     -H "X-API-Key: your-api-key-here" \
     -d '{
       "message": "你好，世界！",
       "model": "llama2",
       "temperature": 0.7,
       "max_tokens": 100
     }'

# 使用查询参数传递API Key
curl "http://127.0.0.1:8000/models?api_key=your-api-key-here"
```

## API Key 认证

### 生成API Key

```bash
# 生成随机API Key
python api_server.py --generate-key

# 或使用启动脚本生成
python start_with_key.py --generate-key
```

### 设置API Key

有多种方式设置API Key：

1. **命令行参数**：
   ```bash
   python api_server.py --api-key your-api-key-here
   python start_with_key.py --api-key your-api-key-here
   ```

2. **环境变量**：
   ```bash
   export OLLAMA_API_KEY=your-api-key-here
   python api_server.py
   ```

3. **交互式设置**：
   ```bash
   python start_with_key.py  # 会提示输入API Key
   ```

### 客户端认证

客户端支持多种认证方式：

1. **HTTP Header (推荐)**：
   ```bash
   curl -H "X-API-Key: your-api-key-here" http://127.0.0.1:8000/models
   ```

2. **Bearer Token**：
   ```bash
   curl -H "Authorization: Bearer your-api-key-here" http://127.0.0.1:8000/models
   ```

3. **查询参数**：
   ```bash
   curl "http://127.0.0.1:8000/models?api_key=your-api-key-here"
   ```

4. **Python客户端**：
   ```python
   from client import OllamaClient

   # 使用API Key创建客户端
   client = OllamaClient(api_key="your-api-key-here")

   # 或使用环境变量
   import os
   client = OllamaClient(api_key=os.getenv("OLLAMA_API_KEY"))
   ```

### 安全注意事项

- 🔒 **保护API Key**：不要在代码中硬编码API Key
- 🔒 **使用环境变量**：推荐使用环境变量存储API Key
- 🔒 **定期更换**：定期生成新的API Key
- 🔒 **HTTPS部署**：生产环境请使用HTTPS

## 支持的任务类型

业务逻辑处理器支持以下任务类型：

1. **CHAT**: 普通对话
2. **SUMMARIZE**: 文本总结
3. **TRANSLATE**: 文本翻译
4. **CODE_REVIEW**: 代码审查
5. **CREATIVE_WRITING**: 创意写作

## 配置说明

可以通过修改 `config.py` 文件来调整配置：

```python
# API服务配置
API_HOST = "127.0.0.1"
API_PORT = 8000

# Ollama配置
OLLAMA_HOST = "127.0.0.1"
OLLAMA_PORT = 11434

# 默认模型
DEFAULT_MODEL = "llama2"

# 请求超时设置
REQUEST_TIMEOUT = 60
```

## 故障排除

### 常见问题

1. **Ollama服务未启动**
   ```bash
   # 检查Ollama是否运行
   curl http://127.0.0.1:11434/api/tags

   # 如果没有响应，启动Ollama
   ollama serve
   ```

2. **没有可用模型**
   ```bash
   # 下载模型
   ollama pull llama2
   ollama pull qwen:7b
   ```

3. **端口被占用**
   - 修改 `config.py` 中的 `API_PORT` 设置
   - 或者停止占用端口的其他服务

4. **依赖包安装失败**
   ```bash
   # 升级pip
   pip install --upgrade pip

   # 使用国内镜像
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

### 日志调试

服务运行时会输出详细日志，可以通过日志信息定位问题：

```bash
# 查看API服务器日志
python api_server.py

# 查看客户端调用日志
python client.py
```

## 性能优化

1. **模型选择**: 根据需求选择合适大小的模型
   - 小模型（如7B）：响应快，资源占用少
   - 大模型（如13B+）：质量高，但需要更多资源

2. **参数调优**:
   - `temperature`: 控制输出随机性（0.1-1.0）
   - `max_tokens`: 限制输出长度，减少等待时间

3. **批量处理**: 使用业务逻辑处理器的批量功能提高效率

## 扩展开发

### 添加新的任务类型

1. 在 `business_logic.py` 中的 `TaskType` 枚举添加新类型
2. 在 `task_templates` 字典中添加对应的提示词模板
3. 根据需要扩展处理逻辑

### 集成到现有项目

```python
# 在你的项目中导入客户端
from client import OllamaClient, ChatRequest

# 创建全局客户端实例
llm_client = OllamaClient()

# 在业务代码中使用
def process_user_query(query: str) -> str:
    request = ChatRequest(message=query, model="llama2")
    response = llm_client.chat(request)
    return response.response if response.status == 'success' else "处理失败"
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
