#!/usr/bin/env python3
"""
演示脚本 - 展示完整的使用流程
"""

import time
from client import OllamaClient, ChatRequest
from business_logic import BusinessLogicProcessor, BusinessTask, TaskType

def print_separator(title: str):
    """打印分隔符"""
    print(f"\n{'='*50}")
    print(f" {title}")
    print(f"{'='*50}")

def demo_basic_usage():
    """演示基本使用"""
    print_separator("基本使用演示")
    
    # 创建客户端
    client = OllamaClient()
    
    # 1. 健康检查
    print("1. 健康检查...")
    health = client.health_check()
    print(f"   状态: {health.get('status')}")
    print(f"   信息: {health.get('message', health.get('error'))}")
    
    if health.get('status') != 'success':
        print("   ⚠️ 服务不可用，请检查Ollama是否正在运行")
        return False
    
    # 2. 获取模型
    print("\n2. 获取可用模型...")
    models = client.get_models()
    if not models:
        print("   ⚠️ 没有可用模型，请先下载模型：ollama pull llama2")
        return False
    
    print(f"   可用模型: {', '.join(models)}")
    test_model = models[0]
    
    # 3. 简单对话
    print(f"\n3. 简单对话测试（使用模型: {test_model}）...")
    request = ChatRequest(
        message="请用一句话介绍你自己",
        model=test_model,
        max_tokens=100
    )
    
    response = client.chat(request)
    if response.status == 'success':
        print(f"   回答: {response.response}")
    else:
        print(f"   ❌ 对话失败: {response.error}")
        return False
    
    return True, test_model

def demo_business_logic(model: str):
    """演示业务逻辑处理"""
    print_separator("业务逻辑处理演示")
    
    client = OllamaClient()
    processor = BusinessLogicProcessor(client)
    
    # 1. 不同类型的任务
    print("1. 处理不同类型的任务...")
    
    tasks = [
        BusinessTask(
            task_id="chat_demo",
            task_type=TaskType.CHAT,
            input_text="什么是机器学习？请简单解释。",
            model=model,
            max_tokens=150
        ),
        BusinessTask(
            task_id="summarize_demo",
            task_type=TaskType.SUMMARIZE,
            input_text="人工智能是一门综合性学科，涉及计算机科学、数学、心理学、哲学等多个领域。它的目标是创造出能够模拟人类智能行为的机器和系统。人工智能的应用领域非常广泛，包括自然语言处理、计算机视觉、机器学习、专家系统等。随着技术的不断发展，人工智能正在改变我们的生活和工作方式。",
            model=model,
            temperature=0.5,
            max_tokens=100
        ),
        BusinessTask(
            task_id="creative_demo",
            task_type=TaskType.CREATIVE_WRITING,
            input_text="未来的智能家居",
            model=model,
            temperature=0.8,
            max_tokens=200
        )
    ]
    
    results = processor.process_batch_tasks(tasks)
    
    for result in results:
        print(f"\n   任务: {result.task_id} ({result.task_type.value})")
        print(f"   状态: {'✅ 成功' if result.success else '❌ 失败'}")
        print(f"   耗时: {result.execution_time:.2f}秒")
        if result.success:
            print(f"   结果: {result.result[:100]}{'...' if len(result.result) > 100 else ''}")
        else:
            print(f"   错误: {result.error}")

def demo_conversation_chain(model: str):
    """演示对话链"""
    print_separator("对话链演示")
    
    client = OllamaClient()
    processor = BusinessLogicProcessor(client)
    
    print("模拟一个关于Python编程的对话...")
    
    messages = [
        "我想学习Python编程，应该从哪里开始？",
        "Python的主要特点是什么？",
        "能给我一个简单的Python代码示例吗？"
    ]
    
    results = processor.create_conversation_chain(messages, model)
    
    for i, (message, result) in enumerate(zip(messages, results)):
        print(f"\n   轮次 {i+1}:")
        print(f"   用户: {message}")
        if result.success:
            print(f"   助手: {result.result}")
            print(f"   (耗时: {result.execution_time:.2f}秒)")
        else:
            print(f"   ❌ 错误: {result.error}")

def demo_advanced_features(model: str):
    """演示高级功能"""
    print_separator("高级功能演示")
    
    client = OllamaClient()
    
    print("1. 参数调优演示...")
    
    # 不同温度设置的对比
    base_message = "写一个关于春天的句子"
    temperatures = [0.1, 0.5, 0.9]
    
    for temp in temperatures:
        print(f"\n   温度设置: {temp}")
        request = ChatRequest(
            message=base_message,
            model=model,
            temperature=temp,
            max_tokens=50
        )
        
        response = client.chat(request)
        if response.status == 'success':
            print(f"   结果: {response.response}")
        else:
            print(f"   ❌ 失败: {response.error}")
    
    print("\n2. 文本生成 vs 对话模式...")
    
    prompt = "解释什么是深度学习"
    
    # 对话模式
    print(f"\n   对话模式:")
    chat_request = ChatRequest(message=prompt, model=model, max_tokens=100)
    chat_response = client.chat(chat_request)
    if chat_response.status == 'success':
        print(f"   {chat_response.response[:100]}...")
    
    # 生成模式
    print(f"\n   生成模式:")
    gen_response = client.generate(chat_request)
    if gen_response.status == 'success':
        print(f"   {gen_response.response[:100]}...")

def main():
    """主演示函数"""
    print("🚀 Ollama 本地大语言模型 API 服务演示")
    print("=" * 60)
    
    # 基本使用演示
    result = demo_basic_usage()
    if not result:
        print("\n❌ 基本服务不可用，演示终止")
        print("\n请确保:")
        print("1. Ollama服务正在运行: ollama serve")
        print("2. 已下载模型: ollama pull llama2")
        print("3. API服务器正在运行: python api_server.py")
        return
    
    success, model = result
    if not success:
        return
    
    # 等待一下，避免请求过快
    time.sleep(1)
    
    # 业务逻辑演示
    demo_business_logic(model)
    
    time.sleep(1)
    
    # 对话链演示
    demo_conversation_chain(model)
    
    time.sleep(1)
    
    # 高级功能演示
    demo_advanced_features(model)
    
    print_separator("演示完成")
    print("🎉 所有演示已完成！")
    print("\n📚 更多功能请参考:")
    print("   - API文档: http://127.0.0.1:8000/docs")
    print("   - 客户端测试: python client.py")
    print("   - 业务逻辑测试: python business_logic.py")
    print("   - 集成测试: python test_integration.py")

if __name__ == "__main__":
    main()
