#!/usr/bin/env python3
"""
带API Key的启动脚本
"""

import argparse
import os
import subprocess
import sys
import time
import requests
from pathlib import Path

def generate_api_key():
    """生成随机API Key"""
    import secrets
    import string
    
    alphabet = string.ascii_letters + string.digits + "-_"
    api_key = "ollama-" + "".join(secrets.choice(alphabet) for _ in range(32))
    return api_key

def check_dependencies():
    """检查依赖"""
    print("📦 检查依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt", "-q"])
        print("✅ 依赖检查完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败")
        return False

def check_ollama_service():
    """检查Ollama服务"""
    print("🔍 检查Ollama服务...")
    try:
        response = requests.get("http://127.0.0.1:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama服务运行中，可用模型: {len(models)}")
            if models:
                for model in models[:3]:
                    print(f"   - {model['name']}")
            return True
        else:
            print("⚠️  Ollama服务响应异常")
            return False
    except requests.exceptions.RequestException:
        print("❌ Ollama服务未运行")
        print("\n请先启动Ollama服务:")
        print("  1. 安装Ollama: curl -fsSL https://ollama.ai/install.sh | sh")
        print("  2. 下载模型: ollama pull llama2")
        print("  3. 启动服务: ollama serve")
        return False

def start_server_with_key(api_key, host="127.0.0.1", port=8000, no_reload=False):
    """启动带API Key的服务器"""
    print(f"🚀 启动API服务器...")
    print(f"   Host: {host}")
    print(f"   Port: {port}")
    print(f"   API Key: {api_key}")
    
    # 构建启动命令
    cmd = [
        sys.executable, "api_server.py",
        "--api-key", api_key,
        "--host", host,
        "--port", str(port)
    ]
    
    if no_reload:
        cmd.append("--no-reload")
    
    try:
        # 启动服务器
        process = subprocess.Popen(cmd)
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        time.sleep(3)
        
        # 检查服务器状态
        try:
            response = requests.get(f"http://{host}:{port}/health", timeout=5)
            if response.status_code == 200:
                print("✅ API服务器启动成功!")
                print(f"   服务地址: http://{host}:{port}")
                print(f"   API文档: http://{host}:{port}/docs")
                print(f"   健康检查: http://{host}:{port}/health")
                return process, api_key
            else:
                print("⚠️  API服务器启动异常")
                process.terminate()
                return None, None
        except requests.exceptions.RequestException:
            print("❌ API服务器启动失败")
            process.terminate()
            return None, None
            
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        return None, None

def test_api_with_key(api_key, host="127.0.0.1", port=8000):
    """测试API调用"""
    print(f"\n🧪 测试API调用...")
    
    base_url = f"http://{host}:{port}"
    headers = {
        "X-API-Key": api_key,
        "Content-Type": "application/json"
    }
    
    try:
        # 测试健康检查
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ 健康检查通过")
        
        # 测试获取模型（需要认证）
        response = requests.get(f"{base_url}/models", headers=headers)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ 模型列表获取成功: {len(models)} 个模型")
        elif response.status_code == 401:
            print("❌ API Key认证失败")
            return False
        
        # 测试简单对话（需要认证）
        if models:
            chat_data = {
                "message": "Hello, please respond with 'API test successful'",
                "model": models[0],
                "max_tokens": 50
            }
            response = requests.post(f"{base_url}/chat", json=chat_data, headers=headers)
            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    print("✅ 对话测试成功")
                    print(f"   回答: {result.get('response', '')[:50]}...")
                else:
                    print(f"⚠️  对话测试失败: {result.get('error')}")
            else:
                print(f"❌ 对话请求失败: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Ollama API Server Launcher with API Key")
    parser.add_argument(
        "--api-key", 
        type=str,
        help="API Key for authentication"
    )
    parser.add_argument(
        "--generate-key", 
        action="store_true",
        help="Generate a new API key"
    )
    parser.add_argument(
        "--host", 
        type=str, 
        default="127.0.0.1",
        help="Host to bind the server (default: 127.0.0.1)"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        default=8000,
        help="Port to bind the server (default: 8000)"
    )
    parser.add_argument(
        "--no-reload", 
        action="store_true",
        help="Disable auto-reload"
    )
    parser.add_argument(
        "--test-only", 
        action="store_true",
        help="Only run tests, don't start server"
    )
    parser.add_argument(
        "--skip-checks", 
        action="store_true",
        help="Skip dependency and Ollama checks"
    )
    
    args = parser.parse_args()
    
    print("🔑 Ollama API Server with API Key Authentication")
    print("=" * 60)
    
    # 生成API Key
    if args.generate_key:
        new_key = generate_api_key()
        print(f"🔑 Generated API Key: {new_key}")
        print(f"\n📋 Usage examples:")
        print(f"   python start_with_key.py --api-key {new_key}")
        print(f"   export OLLAMA_API_KEY={new_key} && python api_server.py")
        return
    
    # 获取API Key
    api_key = args.api_key
    if not api_key:
        api_key = os.getenv("OLLAMA_API_KEY")
    
    if not api_key:
        print("⚠️  No API Key provided!")
        print("Options:")
        print("  1. Generate a new random key")
        print("  2. Enter your own key")
        print("  3. Use default demo key")
        
        choice = input("Choose option (1/2/3): ").strip()
        
        if choice == "1":
            api_key = generate_api_key()
            print(f"🔑 Generated: {api_key}")
        elif choice == "2":
            api_key = input("Enter your API Key: ").strip()
        elif choice == "3":
            api_key = "ollama-demo-key-2024"
            print(f"🔑 Using demo key: {api_key}")
        else:
            print("❌ Invalid choice")
            return
    
    if not api_key:
        print("❌ No API Key provided")
        return
    
    # 检查依赖和服务
    if not args.skip_checks:
        if not check_dependencies():
            return
        
        if not check_ollama_service():
            return
    
    # 仅测试模式
    if args.test_only:
        print(f"\n🧪 Testing with API Key: {api_key}")
        # 假设服务器已经在运行
        if test_api_with_key(api_key, args.host, args.port):
            print("✅ 所有测试通过!")
        else:
            print("❌ 测试失败")
        return
    
    # 启动服务器
    process, final_api_key = start_server_with_key(
        api_key, args.host, args.port, args.no_reload
    )
    
    if process and final_api_key:
        print(f"\n🎉 服务启动成功!")
        print(f"🔑 API Key: {final_api_key}")
        print(f"📝 请保存此API Key用于客户端认证")
        print(f"\n📚 使用示例:")
        print(f"   export OLLAMA_API_KEY={final_api_key}")
        print(f"   python client.py")
        print(f"   python business_logic.py")
        print(f"\n🌐 HTTP调用示例:")
        print(f'   curl -H "X-API-Key: {final_api_key}" http://{args.host}:{args.port}/models')
        
        # 运行简单测试
        time.sleep(2)
        test_api_with_key(final_api_key, args.host, args.port)
        
        print(f"\n⏹️  按 Ctrl+C 停止服务器")
        
        try:
            process.wait()
        except KeyboardInterrupt:
            print(f"\n🛑 正在停止服务器...")
            process.terminate()
            process.wait()
            print("✅ 服务器已停止")
    else:
        print("❌ 服务启动失败")

if __name__ == "__main__":
    main()
