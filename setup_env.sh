#!/bin/bash

# Ollama 本地大语言模型 API 服务环境变量配置
# 类似于 ANTHROPIC_BASE_URL 和 ANTHROPIC_AUTH_TOKEN 的设置方式

# API 服务基础URL
export OLLAMA_BASE_URL=http://127.0.0.1:8000

# API 认证令牌 (API Key)
export OLLAMA_AUTH_TOKEN=ollama-qofpBPKHark0yGUSJ4E_wy3esdfulc5j

# 兼容性环境变量 (与客户端代码兼容)
export OLLAMA_API_KEY=ollama-qofpBPKHark0yGUSJ4E_wy3esdfulc5j

# 显示配置信息
echo "🔑 Ollama API 服务环境变量已设置:"
echo "   OLLAMA_BASE_URL=$OLLAMA_BASE_URL"
echo "   OLLAMA_AUTH_TOKEN=$OLLAMA_AUTH_TOKEN"
echo "   OLLAMA_API_KEY=$OLLAMA_API_KEY"
echo ""
echo "📝 使用方法:"
echo "   source setup_env.sh"
echo "   python3 client.py"
echo "   python3 demo.py"
echo ""
echo "🌐 HTTP 调用示例:"
echo "   curl -H \"X-API-Key: \$OLLAMA_AUTH_TOKEN\" \$OLLAMA_BASE_URL/models"
echo "   curl -H \"Authorization: Bearer \$OLLAMA_AUTH_TOKEN\" \$OLLAMA_BASE_URL/models"
