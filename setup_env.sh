#!/bin/bash

# Ollama 本地大语言模型 API 服务环境变量配置
# 完全兼容 ANTHROPIC_BASE_URL 和 ANTHROPIC_AUTH_TOKEN 格式

# ANTHROPIC 兼容格式 (推荐使用)
export ANTHROPIC_BASE_URL=http://127.0.0.1:8001
export ANTHROPIC_AUTH_TOKEN=ollama-qofpBPKHark0yGUSJ4E_wy3esdfulc5j

# 备用格式 (向后兼容)
export OLLAMA_BASE_URL=http://127.0.0.1:8000
export OLLAMA_AUTH_TOKEN=ollama-qofpBPKHark0yGUSJ4E_wy3esdfulc5j
export OLLAMA_API_KEY=ollama-qofpBPKHark0yGUSJ4E_wy3esdfulc5j

# 显示配置信息
echo "🔑 ANTHROPIC兼容的Ollama API服务环境变量已设置:"
echo "   ANTHROPIC_BASE_URL=$ANTHROPIC_BASE_URL"
echo "   ANTHROPIC_AUTH_TOKEN=$ANTHROPIC_AUTH_TOKEN"
echo ""
echo "📝 使用方法:"
echo "   source setup_env.sh"
echo "   python3 client.py"
echo "   python3 demo.py"
echo ""
echo "🌐 ANTHROPIC格式HTTP调用示例:"
echo "   curl -H \"Authorization: Bearer \$ANTHROPIC_AUTH_TOKEN\" \$ANTHROPIC_BASE_URL/v1/models"
echo "   curl -X POST \"\$ANTHROPIC_BASE_URL/v1/messages\" \\"
echo "        -H \"Authorization: Bearer \$ANTHROPIC_AUTH_TOKEN\" \\"
echo "        -H \"Content-Type: application/json\" \\"
echo "        -d '{\"model\": \"gemma3:1b\", \"max_tokens\": 100, \"messages\": [{\"role\": \"user\", \"content\": \"Hello\"}]}'"
