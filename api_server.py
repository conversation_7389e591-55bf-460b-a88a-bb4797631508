"""
FastAPI服务器，封装Ollama本地模型调用
"""

import argparse
import logging
import os
import sys
import traceback
from typing import List, Optional
from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
import ollama
import uvicorn

from config import API_HOST, API_PORT, DEFAULT_MODEL, LOG_LEVEL, API_KEY_HEADER, DEFAULT_API_KEY

# 全局变量存储API Key
CURRENT_API_KEY = ""

# 配置日志
logging.basicConfig(level=getattr(logging, LOG_LEVEL))
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="Ollama API Server",
    description="本地大语言模型API服务（支持API Key认证）",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API Key认证依赖
security = HTTPBearer(auto_error=False)

def verify_api_key(request: Request, credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)):
    """验证API Key"""
    if not CURRENT_API_KEY:
        # 如果没有设置API Key，则不需要认证
        return True

    # 检查Authorization header (Bearer token)
    if credentials and credentials.credentials == CURRENT_API_KEY:
        return True

    # 检查自定义header
    api_key = request.headers.get(API_KEY_HEADER)
    if api_key == CURRENT_API_KEY:
        return True

    # 检查查询参数
    api_key = request.query_params.get("api_key")
    if api_key == CURRENT_API_KEY:
        return True

    raise HTTPException(
        status_code=401,
        detail="Invalid API key. Please provide a valid API key in Authorization header, X-API-Key header, or api_key query parameter."
    )

# 请求和响应模型
class ChatRequest(BaseModel):
    message: str
    model: Optional[str] = DEFAULT_MODEL
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 1000

class ChatResponse(BaseModel):
    response: str
    status: str
    model: str
    error: Optional[str] = None

class ModelsResponse(BaseModel):
    models: List[str]
    status: str
    error: Optional[str] = None

class HealthResponse(BaseModel):
    status: str
    message: str

@app.get("/", response_model=HealthResponse)
async def root():
    """根路径健康检查（无需认证）"""
    auth_status = "enabled" if CURRENT_API_KEY else "disabled"
    return HealthResponse(
        status="success",
        message=f"Ollama API Server is running (API Key authentication: {auth_status})"
    )

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查接口（无需认证）"""
    try:
        # 尝试连接Ollama服务
        models = ollama.list()
        auth_status = "enabled" if CURRENT_API_KEY else "disabled"
        return HealthResponse(
            status="success",
            message=f"Ollama service is running with {len(models['models'])} models (API Key: {auth_status})"
        )
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return HealthResponse(
            status="error",
            message=f"Ollama service is not available: {str(e)}"
        )

@app.get("/models", response_model=ModelsResponse)
async def get_models(authenticated: bool = Depends(verify_api_key)):
    """获取可用模型列表（需要API Key认证）"""
    try:
        models_data = ollama.list()
        model_names = [model['name'] for model in models_data['models']]
        logger.info(f"Available models: {model_names}")

        return ModelsResponse(
            models=model_names,
            status="success"
        )
    except Exception as e:
        error_msg = f"Failed to get models: {str(e)}"
        logger.error(error_msg)
        return ModelsResponse(
            models=[],
            status="error",
            error=error_msg
        )

@app.post("/chat", response_model=ChatResponse)
async def chat_with_model(request: ChatRequest, authenticated: bool = Depends(verify_api_key)):
    """与大语言模型对话（需要API Key认证）"""
    try:
        logger.info(f"Chat request - Model: {request.model}, Message: {request.message[:100]}...")
        
        # 调用Ollama模型
        response = ollama.chat(
            model=request.model,
            messages=[
                {
                    'role': 'user',
                    'content': request.message,
                }
            ],
            options={
                'temperature': request.temperature,
                'num_predict': request.max_tokens,
            }
        )
        
        model_response = response['message']['content']
        logger.info(f"Model response length: {len(model_response)}")
        
        return ChatResponse(
            response=model_response,
            status="success",
            model=request.model
        )
        
    except Exception as e:
        error_msg = f"Chat failed: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        
        return ChatResponse(
            response="",
            status="error",
            model=request.model,
            error=error_msg
        )

@app.post("/generate", response_model=ChatResponse)
async def generate_text(request: ChatRequest, authenticated: bool = Depends(verify_api_key)):
    """文本生成接口（需要API Key认证）"""
    try:
        logger.info(f"Generate request - Model: {request.model}, Prompt: {request.message[:100]}...")
        
        # 使用ollama.generate进行文本生成
        response = ollama.generate(
            model=request.model,
            prompt=request.message,
            options={
                'temperature': request.temperature,
                'num_predict': request.max_tokens,
            }
        )
        
        generated_text = response['response']
        logger.info(f"Generated text length: {len(generated_text)}")
        
        return ChatResponse(
            response=generated_text,
            status="success",
            model=request.model
        )
        
    except Exception as e:
        error_msg = f"Generation failed: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        
        return ChatResponse(
            response="",
            status="error",
            model=request.model,
            error=error_msg
        )

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Ollama API Server with API Key authentication")
    parser.add_argument(
        "--api-key",
        type=str,
        default="",
        help="API Key for authentication (can also be set via OLLAMA_API_KEY environment variable)"
    )
    parser.add_argument(
        "--host",
        type=str,
        default=API_HOST,
        help=f"Host to bind the server (default: {API_HOST})"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=API_PORT,
        help=f"Port to bind the server (default: {API_PORT})"
    )
    parser.add_argument(
        "--no-reload",
        action="store_true",
        help="Disable auto-reload in development mode"
    )
    parser.add_argument(
        "--generate-key",
        action="store_true",
        help="Generate a random API key and exit"
    )
    return parser.parse_args()

def generate_api_key():
    """生成随机API Key"""
    import secrets
    import string

    # 生成32位随机字符串
    alphabet = string.ascii_letters + string.digits + "-_"
    api_key = "ollama-" + "".join(secrets.choice(alphabet) for _ in range(32))
    return api_key

if __name__ == "__main__":
    args = parse_arguments()

    # 如果请求生成API Key
    if args.generate_key:
        new_key = generate_api_key()
        print(f"Generated API Key: {new_key}")
        print(f"\nUsage examples:")
        print(f"  python api_server.py --api-key {new_key}")
        print(f"  export OLLAMA_API_KEY={new_key} && python api_server.py")
        sys.exit(0)

    # 设置API Key
    global CURRENT_API_KEY
    if args.api_key:
        CURRENT_API_KEY = args.api_key
    elif os.getenv("OLLAMA_API_KEY"):
        CURRENT_API_KEY = os.getenv("OLLAMA_API_KEY")
    else:
        # 如果没有提供API Key，询问用户是否使用默认Key或生成新Key
        print("⚠️  No API Key provided!")
        print("Options:")
        print(f"  1. Use default demo key: {DEFAULT_API_KEY}")
        print("  2. Generate a new random key")
        print("  3. Run without authentication (not recommended)")

        choice = input("Choose option (1/2/3): ").strip()

        if choice == "1":
            CURRENT_API_KEY = DEFAULT_API_KEY
        elif choice == "2":
            CURRENT_API_KEY = generate_api_key()
            print(f"Generated API Key: {CURRENT_API_KEY}")
        elif choice == "3":
            CURRENT_API_KEY = ""
            print("⚠️  Running without API Key authentication!")
        else:
            print("Invalid choice. Using default demo key.")
            CURRENT_API_KEY = DEFAULT_API_KEY

    # 显示启动信息
    if CURRENT_API_KEY:
        logger.info(f"API Key authentication enabled")
        logger.info(f"API Key: {CURRENT_API_KEY}")
        print(f"\n🔑 API Key: {CURRENT_API_KEY}")
        print(f"📝 Save this key for client authentication!")
    else:
        logger.warning("API Key authentication disabled")
        print(f"\n⚠️  API Key authentication is DISABLED")

    logger.info(f"Starting Ollama API Server on {args.host}:{args.port}")
    print(f"🚀 Server starting on http://{args.host}:{args.port}")
    print(f"📚 API Documentation: http://{args.host}:{args.port}/docs")

    uvicorn.run(
        "api_server:app",
        host=args.host,
        port=args.port,
        reload=not args.no_reload,
        log_level=LOG_LEVEL.lower()
    )
