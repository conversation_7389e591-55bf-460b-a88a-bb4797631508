"""
FastAPI服务器，封装Ollama本地模型调用
"""

import logging
import traceback
from typing import List, Optional
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import ollama
import uvicorn

from config import API_HOST, API_PORT, DEFAULT_MODEL, LOG_LEVEL

# 配置日志
logging.basicConfig(level=getattr(logging, LOG_LEVEL))
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="Ollama API Server",
    description="本地大语言模型API服务",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求和响应模型
class ChatRequest(BaseModel):
    message: str
    model: Optional[str] = DEFAULT_MODEL
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 1000

class ChatResponse(BaseModel):
    response: str
    status: str
    model: str
    error: Optional[str] = None

class ModelsResponse(BaseModel):
    models: List[str]
    status: str
    error: Optional[str] = None

class HealthResponse(BaseModel):
    status: str
    message: str

@app.get("/", response_model=HealthResponse)
async def root():
    """根路径健康检查"""
    return HealthResponse(status="success", message="Ollama API Server is running")

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查接口"""
    try:
        # 尝试连接Ollama服务
        models = ollama.list()
        return HealthResponse(
            status="success", 
            message=f"Ollama service is running with {len(models['models'])} models"
        )
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return HealthResponse(
            status="error", 
            message=f"Ollama service is not available: {str(e)}"
        )

@app.get("/models", response_model=ModelsResponse)
async def get_models():
    """获取可用模型列表"""
    try:
        models_data = ollama.list()
        model_names = [model['name'] for model in models_data['models']]
        logger.info(f"Available models: {model_names}")
        
        return ModelsResponse(
            models=model_names,
            status="success"
        )
    except Exception as e:
        error_msg = f"Failed to get models: {str(e)}"
        logger.error(error_msg)
        return ModelsResponse(
            models=[],
            status="error",
            error=error_msg
        )

@app.post("/chat", response_model=ChatResponse)
async def chat_with_model(request: ChatRequest):
    """与大语言模型对话"""
    try:
        logger.info(f"Chat request - Model: {request.model}, Message: {request.message[:100]}...")
        
        # 调用Ollama模型
        response = ollama.chat(
            model=request.model,
            messages=[
                {
                    'role': 'user',
                    'content': request.message,
                }
            ],
            options={
                'temperature': request.temperature,
                'num_predict': request.max_tokens,
            }
        )
        
        model_response = response['message']['content']
        logger.info(f"Model response length: {len(model_response)}")
        
        return ChatResponse(
            response=model_response,
            status="success",
            model=request.model
        )
        
    except Exception as e:
        error_msg = f"Chat failed: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        
        return ChatResponse(
            response="",
            status="error",
            model=request.model,
            error=error_msg
        )

@app.post("/generate", response_model=ChatResponse)
async def generate_text(request: ChatRequest):
    """文本生成接口（流式响应的简化版本）"""
    try:
        logger.info(f"Generate request - Model: {request.model}, Prompt: {request.message[:100]}...")
        
        # 使用ollama.generate进行文本生成
        response = ollama.generate(
            model=request.model,
            prompt=request.message,
            options={
                'temperature': request.temperature,
                'num_predict': request.max_tokens,
            }
        )
        
        generated_text = response['response']
        logger.info(f"Generated text length: {len(generated_text)}")
        
        return ChatResponse(
            response=generated_text,
            status="success",
            model=request.model
        )
        
    except Exception as e:
        error_msg = f"Generation failed: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        
        return ChatResponse(
            response="",
            status="error",
            model=request.model,
            error=error_msg
        )

if __name__ == "__main__":
    logger.info(f"Starting Ollama API Server on {API_HOST}:{API_PORT}")
    uvicorn.run(
        "api_server:app",
        host=API_HOST,
        port=API_PORT,
        reload=True,
        log_level=LOG_LEVEL.lower()
    )
